<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>br.com.celk</groupId>
        <artifactId>saude-wicket</artifactId>
<version>3.1.282.1-SNAPSHOT</version>
    </parent>

    <artifactId>saude-web-processors</artifactId>
    <name>saude-web-processors</name>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <verbose>true</verbose>
                    <fork>true</fork>
                    <compilerArgument>-proc:none</compilerArgument>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.wicketstuff</groupId>
            <artifactId>wicketstuff-annotation</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jvnet.sorcerer</groupId>
            <artifactId>sorcerer-javac</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
