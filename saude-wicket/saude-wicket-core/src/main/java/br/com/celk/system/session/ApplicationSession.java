package br.com.celk.system.session;

import br.com.celk.component.favoritos.FavoritosCache;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.ISessaoAplicacao;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.Session;
import org.apache.wicket.protocol.http.WebSession;
import org.apache.wicket.request.Request;

import javax.servlet.http.HttpSessionBindingEvent;
import javax.servlet.http.HttpSessionBindingListener;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ApplicationSession extends WebSession implements HttpSessionBindingListener, ISessaoAplicacao {

    public static final String ATTRIBUTE_QTD_MENSAGEM = "quantidadeMensagensUsuario";
    public static final String ASYNC_CACHE = "asyncCache";
    public static final String MENSAGENS_NOVAS_CACHE = "mensagensNovasCache";
    private AbstractSessaoAplicacao sessaoAplicacao;
    private AbstractSessaoAplicacao authorizedSession;
    private FavoritosCache favoritosCache = new FavoritosCache();
    private String lastRequestedUrl;
    private Date lastRequestDate;
    private String lastIp;
    private Date dataAutenticacao;
    private String versao;
    private Long limiteSessao;

    public ApplicationSession(Request request) {
        super(request);
    }

    public static ApplicationSession get() {
        return (ApplicationSession) Session.get();
    }

    public boolean isAuthenticated() {
        return sessaoAplicacao != null;
    }

    @Override
    public AbstractSessaoAplicacao getSession() {
        return getAuthorizedSession() != null ? getAuthorizedSession() : getSessaoAplicacao();
    }

    public AbstractSessaoAplicacao getSessaoAplicacao() {
        return sessaoAplicacao;
    }

    public void setSessaoAplicacao(AbstractSessaoAplicacao sessaoAplicacao) {
        this.sessaoAplicacao = sessaoAplicacao;
    }

    public AbstractSessaoAplicacao getAuthorizedSession() {
        return authorizedSession;
    }

    public void setAuthorizedSession(AbstractSessaoAplicacao authorizedSession) {
        this.authorizedSession = authorizedSession;
    }

    public synchronized Map<AsyncProcess, String> getAsyncCache() {
        Map<AsyncProcess, String> asyncCache = (Map<AsyncProcess, String>) getAttribute(ASYNC_CACHE);
        if (asyncCache == null) {
            setAttribute(ASYNC_CACHE, new LinkedHashMap<AsyncProcess, String>());
        }
        return (Map<AsyncProcess, String>) getAttribute(ASYNC_CACHE);
    }

    public synchronized List<Mensagem> getMensagensNovasCache() {
        List<Mensagem> mensagensNovasCache = (List<Mensagem>) getAttribute(MENSAGENS_NOVAS_CACHE);
        if (mensagensNovasCache == null) {
            setAttribute(MENSAGENS_NOVAS_CACHE, new LinkedList<Mensagem>());
        }
        return (List<Mensagem>) getAttribute(MENSAGENS_NOVAS_CACHE);
    }

    public synchronized Long getQuantidadeMensagensUsuario() {
        Long quantidadeMensagensUsuario = (Long) getAttribute(ATTRIBUTE_QTD_MENSAGEM);
        if (quantidadeMensagensUsuario == null) {
            setAttribute(ATTRIBUTE_QTD_MENSAGEM, 0L);
        }
        return (Long) getAttribute(ATTRIBUTE_QTD_MENSAGEM);
    }

    public void subtrairQuantidadeMensagensUsuario() {
        if (getQuantidadeMensagensUsuario() <= 0) return;

        ApplicationSession.get().setAttribute(ApplicationSession.ATTRIBUTE_QTD_MENSAGEM, getQuantidadeMensagensUsuario() - 1);
    }

    public void adicinarQuantidadeMensagensUsuario() {
        ApplicationSession.get().setAttribute(ApplicationSession.ATTRIBUTE_QTD_MENSAGEM, getQuantidadeMensagensUsuario() + 1);
    }

    public FavoritosCache getFavoritosCache() {
        return favoritosCache;
    }

    private long timeSession;

    public void setTimeSession(long timeSession) {
        this.timeSession = timeSession;
    }

    public long getTimeSession() {
        return this.timeSession;
    }

    private String tenant;

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    @Override
    public void valueBound(HttpSessionBindingEvent hsbe) {
    }

    @Override
    public void valueUnbound(HttpSessionBindingEvent hsbe) {
        try {
            if (tenant != null) {
                TenantContext.setContext(tenant);
                BOFactory.getBO(CommomFacade.class).finalizarSessao(getId() + timeSession);
                TenantContext.setContext(null);
            }
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
        }
    }

    public String getLastRequestedUrl() {
        return lastRequestedUrl;
    }

    public void setLastRequestedUrl(String lastRequestedUrl) {
        this.lastRequestedUrl = lastRequestedUrl;
    }

    public Date getLastRequestDate() {
        return lastRequestDate;
    }

    public void setLastRequestDate(Date lastRequestDate) {
        this.lastRequestDate = lastRequestDate;
    }

    public Date getDataAutenticacao() {
        return dataAutenticacao;
    }

    public void setDataAutenticacao(Date dataAutenticacao) {
        this.dataAutenticacao = dataAutenticacao;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

    public String getUpTime() {
        return Data.imprimirIntervaloHoras(dataAutenticacao, DataUtil.getDataAtual());
    }

    public String getLastIp() {
        return lastIp;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public Long getTempoLimite() {
        if (limiteSessao == null) {
            try {
                limiteSessao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("TempoLimiteSessaoAtiva");
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return limiteSessao;
    }
}
