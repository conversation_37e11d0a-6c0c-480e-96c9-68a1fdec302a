package br.com.celk.resources.atendimento;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TipoAtendimentoCssGenerator {

    public String generateCss(){
        List<TipoAtendimento> tipoAtendimentos = LoadManager.getInstance(TipoAtendimento.class)
                .addProperty(VOUtils.montarPath(TipoAtendimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(TipoAtendimento.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(TipoAtendimento.PROP_COR_LISTA_ATENDIMENTO))
                .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimento.PROP_COR_LISTA_ATENDIMENTO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .start().getList();
        
        String css = "";
        
        for (TipoAtendimento tipoAtendimento : tipoAtendimentos) {
            css += " table tr ."+resolveClassName(tipoAtendimento)+"{"
                    + "     background: #"+tipoAtendimento.getCorListaAtendimento()+" ;"
                    + "     padding: 0px 3px 0px 3px;"
                    + "} ";
        }
        
        return css;
    }
    
    public String resolveClassName(TipoAtendimento tipoAtendimento){
        return "tipo"+tipoAtendimento.getCodigo().toString();
    }
    
}
