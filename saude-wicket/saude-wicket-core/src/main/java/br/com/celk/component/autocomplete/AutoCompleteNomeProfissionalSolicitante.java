package br.com.celk.component.autocomplete;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import ch.lambdaj.Lambda;
import ch.lambdaj.collection.LambdaCollection;
import ch.lambdaj.collection.LambdaCollections;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteNomeProfissionalSolicitante extends AutoComplete<String> {

    private List<String> nomes = new ArrayList<String>();

    public AutoCompleteNomeProfissionalSolicitante(String id, IModel object) {
        super(id, object);
        init();
    }

    public AutoCompleteNomeProfissionalSolicitante(String id) {
        super(id);
        init();
    }

    private void init() {
        getAutoCompleteSettings().setThrottleDelay(600);
        add(new AttributeModifier("class", "uppercase"));
    }

    @Override
    protected Iterator<String> getChoices(String input) {
        nomes.clear();
        if (StringUtils.trimToNull(input) != null) {
            List<AtendimentoExame> lstAux = LoadManager.getInstance(AtendimentoExame.class)
                    .addProperty(VOUtils.montarPath(AtendimentoExame.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(AtendimentoExame.PROP_NOME_PROFISSIONAL))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoExame.PROP_NOME_PROFISSIONAL), BuilderQueryCustom.QueryParameter.ILIKE, input))
                    .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoExame.PROP_DATA_EXAME, "desc"))
                    .setMaxResults(10)
                    .start().getList();

//            Collection<AtendimentoExame> selectDistinctArgument = Lambda.selectDistinctArgument(lstAux, Lambda.on(AtendimentoExame.class).getNomeProfissional());
//            nomes = Lambda.extract(selectDistinctArgument, Lambda.on(AtendimentoExame.class).getNomeProfissional());

            LambdaCollection<AtendimentoExame> lambdaCollection = LambdaCollections.with(lstAux);
            nomes = new ArrayList<String>(lambdaCollection.extract(Lambda.on(AtendimentoExame.class).getNomeProfissional()).distinct());

        }
        return nomes.iterator();
    }

    @Override
    public String getTextValue(String object) {
        return object;
    }

}
