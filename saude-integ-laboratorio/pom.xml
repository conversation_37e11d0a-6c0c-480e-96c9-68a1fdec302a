<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>saude-2</artifactId>
        <groupId>br.com.celk</groupId>
<version>3.1.282.2-SNAPSHOT</version>
    </parent>

    <groupId>br.com.celk.integracao.laboratorio</groupId>
    <artifactId>saude-integ-laboratorio</artifactId>
    <packaging>pom</packaging>
    <name>saude-integ-laboratorio</name>

    <modules>
        <module>saude-integ-laboratorio-connect</module>
        <module>saude-integ-laboratorio-ejb</module>
        <module>saude-integ-laboratorio-whebsis-ejb</module>
        <module>saude-integ-laboratorio-whebsis-connect</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--LABORATORIOS-->
            <dependency>
                <groupId>br.com.celk.integracao.laboratorio</groupId>
                <artifactId>saude-integ-laboratorio-whebsis-connect</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--LABORATORIOS-->

            <dependency>
                <groupId>br.com.celk.integracao.laboratorio</groupId>
                <artifactId>saude-integ-laboratorio-connect</artifactId>
                <version>${project.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>br.com.celk.integracao.laboratorio</groupId>-->
<!--                <artifactId>saude-integ-laboratorio-whebsis-connect</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--            </dependency>-->
        </dependencies>
    </dependencyManagement>

</project>
