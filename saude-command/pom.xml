<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>saude-2</artifactId>
        <groupId>br.com.celk</groupId>
<version>3.1.282.2-SNAPSHOT</version>
    </parent>

    <artifactId>saude-command</artifactId>
    <name>saude-command</name>
    <packaging>jar</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.zeroturnaround</groupId>
                <artifactId>jrebel-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-ejb-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>false</addClasspath>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-jaspers</id>
                        <phase>initialize</phase>
                        <configuration>
                            <target>
                                <echo message="copiando Jaspers"/>
                                <copy todir="jasper">
                                    <fileset dir="src/main/java" erroronmissingdir="false">
                                        <include name="**/*.jrxml"/>
                                    </fileset>
                                </copy>
                                <copy todir="target/classes">
                                    <fileset dir="src/main/java" erroronmissingdir="false">
                                        <include name="**/*.jrxml"/>
                                    </fileset>
                                </copy>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>compile-reports</id>
                        <phase>compile</phase>
                        <configuration>
                            <target>
                                <property name="build.classes.dir" value="target/classes"/>
                                <java classname="br.com.ksisolucoes.report.CompilaReports" failonerror="on" fork="true">
                                    <classpath>
                                        <path refid="maven.compile.classpath"/>
                                        <path path="${build.classes.dir}"/>
                                    </classpath>
                                </java>
                                <echo message="copiando JRXMLs"/>
                                <copy todir="target/classes/">
                                    <fileset dir="jasper/" erroronmissingdir="false">
                                        <include name="**/*.jasper"/>
                                    </fileset>
                                </copy>
                                <echo message="copiando Template PDF"/>
                                <copy todir="target/classes">
                                    <fileset dir="src/main/java" erroronmissingdir="false">
                                        <include name="**/*.pdf"/>
                                    </fileset>
                                </copy>
                                <delete>
                                    <fileset dir="target/classes/">
                                        <include name="**/*.jrxml"/>
                                    </fileset>
                                </delete>
                                <echo message="JRXMLs Copiados"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-connect</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-indra-connect</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sqs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.4</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.9.4</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.9.4</version>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>br.com.ksisolucoes.saude</groupId>
            <artifactId>gem-cadsus</artifactId>
        </dependency>
        <dependency>
            <groupId>other</groupId>
            <artifactId>jaybird-full</artifactId>
        </dependency>
        <dependency>
            <groupId>other</groupId>
            <artifactId>xbean</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-provider</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jboss.spec.javax.jms</groupId>
            <artifactId>jboss-jms-api_2.0_spec</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.jboss.as</groupId>
            <artifactId>jboss-as-messaging</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.spec.javax.ejb</groupId>
            <artifactId>jboss-ejb-api_3.2_spec</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>esus-thrift-5.3.0</artifactId>
            <version>5.3.0</version>
        </dependency>
        <dependency>
            <groupId>br.com.celk.tiss</groupId>
            <artifactId>celk-tiss</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk.horus</groupId>
            <artifactId>celk-horus</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis-jaxrpc</artifactId>
        </dependency>
        <dependency>
            <groupId>br.gov.saude</groupId>
            <artifactId>sisprenatalws</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk.nfe</groupId>
            <artifactId>nfe</artifactId>
        </dependency>
        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-web-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-client</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>
        <!-- FIXME remover lib jetbrains -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>celkged</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>horus</artifactId>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>nfe</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
        </dependency>

        <dependency>
            <groupId>br.com.celk.rnds</groupId>
            <artifactId>rnds</artifactId>
            <version>1.0.1</version>
        </dependency>

    </dependencies>
</project>
