/*
 * Created on 25/10/2004
 *
 */
package br.com.ksisolucoes.report;

import br.com.ksisolucoes.report.BaseCompilaReports.Relatorio;
import br.com.ksisolucoes.report.build.ICustomizeJasperDesign;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CompilaReports implements ReportProperties {

    private List<Relatorio> agendas() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_distribuicao_cotas_ppi.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_ficha_agendamento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_ficha_agendamento_quebra_profissional.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_com_ficha.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_DUPLO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_solicitacao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_solicitacao_a4.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_solicitacao_termica.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_cadastro.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_agendamento_sem_solicitacao_com_ficha.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_DUPLO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/sub_relatorio_impressao_comprovante_agendamento_sem_solicitacao_formatacao_grande.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_unidade_cota.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_relacao_agendas_unidade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_relacao_vagas_cotas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relacao_vagas_agenda.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/tempo_medio_fila_espera.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/encaminhamento/jrxml/relatorio_impressao_pedido_tfd.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/encaminhamento/jrxml/relatorio_impressao_pedido_tfd_estado.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/encaminhamento/jrxml/relatorio_impressao_pedido_tfd_estado_pag2.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/encaminhamento/jrxml/relatorio_encaminhados_regional.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/encaminhamento/jrxml/relatorio_encaminhamento_tfd.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/tfd/requisicaoenvioexame/jrxml/relatorio_impressao_comprovante_requisicao_envio_exame.jrxml", null));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/tfd/requisicaoenvioexame/jrxml/relacao_exames_enviados.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/encaminhamento/jrxml/relacao_agendamentos_tfd.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/encaminhamento/jrxml/relacao_pacientes_nao_compareceram_tfd.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relacao_pacientes_nao_compareceram.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/encaminhamento/jrxml/relatorio_impressao_comprovante_recebimento_tfd.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_relacao_agenda.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_relacao_agenda_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_exame_agendamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_resumo_exame_agendamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/grafico_distribuicao_exame_agendamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/grafico_estatisticas_nao_comparecimento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/grafico_distribuicao_mensal_exame_agendamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_impressao_autorizacao_exame.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_impressao_comprovante_entrega_exame.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/sub_relatorio_exames_impressao_comprovante_entrega_exame.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_impressao_comprovante_agendamento_exame_fora_rede.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/solicitacaoagendamento/jrxml/relatorio_comprovante_solicitacao_agendamento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_resumo_solicitacoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_acompanhamento_solicitacoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relacao_solicitacoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relacao_solicitacoes_viagem.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relacao_agenda_contato.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_resumo_exames.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_resumo_procedimentos_solicitados.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_relacao_exames.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/solicitacaoagendamento/jrxml/relatorio_impressao_lote_envio_solicitacoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_resumo_exames_autorizados_valor.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_resumo_exames_autorizados_quantidade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relacao_agendas_canceladas_remanejadas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_relacao_lista_espera.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_impressao_laudo_exame.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/sub_relatorio_impressao_laudo_exame.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_entrega_pedido_tfd.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_resumo_fila_espera.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_monitoramento_solicitacoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_resultado_preventivo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_teste_rapido_dengue.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_exames.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_procedimentos_realizados_prestador_servico.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_fpo_prestador_servicos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_exportacao_agenda_laboratorio.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_procedimentos_realizados_prestador_cons_comp_simplificado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_procedimentos_realizados_prestador_cons_comp_comparativo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> cadSus() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relatorio_usuario_cadsus_sem_domicilio.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relatorio_relacao_usuario_cadsus.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relatorio_profissional.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/ficha_usuario_cadsus.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/sub_ficha_usuario_cadsus_cns.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relacao_usuarios_provisorio.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/impressao_cartao_sus.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relatorio_ficha_paciente.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relacao_familias.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/resumo_familias.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/resumo_familias_faixa_etaria.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/resumo_familias_doenca.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/cadsus/jrxml/relatorio_pacientes_conferencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/comorbidade/jrxml/relacao_pacientes_condicao_situacao_saude.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/comorbidade/jrxml/relatorio_consolidado_pacientes_comorbidades.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relacao_planejamento_visita.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/unidadesaude/relatorio/jrxml/relatorio_resumo_atendimento_cid.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relatorio_paciente_duplicado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/pacientesemcns/jrxml/relatorio_pacientes_sem_cns.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/relatorioidosos/jrxml/relatorio_idosos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_acompanhamento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/relatoriocriancas/jrxml/relatorio_criancas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_relacao_producao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_relacao_producao_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_cartao_identificacao_paciente.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/unidadesaude/relatorio/jrxml/relatorio_relacao_exames_externos_testes_rapidos_covid19.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> basico() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/validacao_relatorios.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_validacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_empresa_equipamento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_relacao_equipes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_producao_vigilancia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_producao_vigilancia_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_producao_vigilancia_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_relacao_carga_horaria.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_impressao_ficha_profissional.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
//        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_equipamento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
//        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_sala_unidade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_feriado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/atividades.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/estado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/tipoEndereco.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/tipoPessoa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_pessoa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_pessoa_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/sub_relatorio_usuariocadsus_ocorrencia.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/samu/jrxml/relatorio_relacao_atendimentos_samu.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/samu/jrxml/relatorio_resumo_atendimentos_samu.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/materiais/brasindice/jrxml/relatorio_relacao_produto_brasindice.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/cadsus/exclusao/jrxml/relatorio_usuario_cadsus_exclusao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> geral() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/jrxml/relatorio_produto_estrutura_equipamento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/jrxml/relatorio_identificacao_nivel_superior.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/jrxml/sub_historico_estrutura.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/jrxml/relatorio_impressao_etiqueta_paciente.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/jrxml/relatorio_historico_estrutura.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/jrxml/relatorio_estrutura_equipamento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/jrxml/impressao_mensagem_interna.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/siab/jrxml/relacao_familias_cadastradas_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/siab/jrxml/relacao_familias_cadastradas_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/siab/jrxml/relacao_movimentacao_familias.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/siab/jrxml/relatorio_acompanhamento_cadastro_familias.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/processo/metascadastrofamilia/jrxml/relacao_metas_nao_cumpridas_cadastro_familia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/geral/despesa/jrxml/relatorio_despesas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/pesquisa/jrxml/grafico_resultado_pesquisa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/pesquisa/jrxml/grafico_resultado_pesquisa_por_resposta.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/pesquisa/jrxml/cruzamento_perguntas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/geral/pesquisa/jrxml/grafico_cruzamento_perguntas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> programaSaude() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/programasaude/jrxml/sub_relatorio_preventivo.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/programasaude/jrxml/relatorio_preventivo.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/programasaude/jrxml/sub_relatorio_histopatologico.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/programasaude/jrxml/relatorio_histopatologico.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/programasaude/jrxml/relatorio_mamografia.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/programasaude/jrxml/relatorio_hiperdia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> prontuario() {
        // <editor-fold defaultstate="collapsed" desc=" Caminho dos Jrxmls ">
        //Caminho dos JRXML
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_impressao_laudo_exame.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_prontuario_eventos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_impressao_prescricao_interna.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_impressao_prescricao_interna_farmacia.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/sub_relatorio_impressao_prescricao_interna_farmacia_kit.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/sub_relatorio_impressao_prescricao_interna_cuidados.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/sub_relatorio_impressao_prescricao_interna_produtos_complementares_padrao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_declaracao_comparecimento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_agenda_especializada.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_agenda_especializada_assinatura_digital.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_prescricao_oculos.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_branca.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_DUAS_VIAS_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_branca_assinatura_digital.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_basica_assinatura_digital_v2.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_a5.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_DUAS_VIAS_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_livre.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_DUAS_VIAS));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_livre_via_unica.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_livre_via_unica_assinatura_digital.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_livre_assinatura_digital.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/sub_relatorio_formulario_aquisicao_medicamentos_nao_padronizados.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/sub_relatorio_formulario_aquisicao_medicamentos_nao_padronizados_landscape.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_item.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/emergencia/jrxml/relatorio_receituario_item_landscape.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_receituario_limite_excedido.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));

        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_impressao_dados_exame.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_impressao_dados_confirmacao_exame.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_particular.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_particular_a5.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_assinado_digitalmente.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_mod.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_a5.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_eletrocardiograma.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_teledermatoscopia.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_teledermatoscopia1.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_teledermatoscopia2.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/examebpai/jrxml/relatorio_impressao_exame_bpai.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exameapac/jrxml/relatorio_impressao_exame_apac.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_encaminhamento_tfd_completo.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_encaminhamento_tfd_completo_interestadual.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_multiplas_receitas.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_multiplas_receitas_landscape.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_hepatite.jrxml", ICustomizeJasperDesign.CAB_LACEN));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_imunologia.jrxml", ICustomizeJasperDesign.CAB_LACEN));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_anti_hcv.jrxml", ICustomizeJasperDesign.CAB_LACEN));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_requisicao_contagem_linfocitos.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_carga_viral_hiv_rna_lacen.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_carga_viral_hiv.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_deteccao_dna_pro_viral_hiv_1.jrxml", ICustomizeJasperDesign.CAB_LACEN));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_HIV.jrxml", ICustomizeJasperDesign.CAB_LACEN));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_exame_tuberculose.jrxml", ICustomizeJasperDesign.CAB_LACEN));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_hiv.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_hepatite_b.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_hepatite_c.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_sifilis.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_covid_19.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_dengue.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_influenza.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_hanseniase.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_hiv_sifilis.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido_tuberculose.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_ficha_teste_rapido.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/testesrapidos/jrxml/relatorio_teste_rapido.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/tabagismo/jrxml/relatorio_formulario_tabagismo.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/tabagismo/jrxml/sub_formulario_tabagismo_historia_patologica_pregressa.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/tabagismo/jrxml/sub_formulario_tabagismo_depressao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/tabagismo/jrxml/sub_formulario_tabagismo_teste_fagerstrom.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/tabagismo/jrxml/sub_formulario_tabagismo_grau_motivacao.jrxml"));

        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_grafico_encaminhamento_situacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_atendimento_odontologico.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_relacao_atendimentos_odontologicos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_perfil_atendimento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_perfil_atendimento_hospital.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_hospital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_micro_area.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_micro_area_hospital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_unidades_atendimento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_unidades_atendimento_hospital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_1_coluna.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_1_coluna_hospital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_usuarios_atendidos_toh_1_coluna_hospital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_codigo.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_codigo_cid.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_alta.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_codigo_cid_hospital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_quantidade.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_quantidade_hospital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_dispensacao_idade.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_vacinas_aplicadas.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_cidades.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_cidades_bairros.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_doencas.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_atividade_grupo.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_classificacao_risco.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_raca.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_encaminhamento_especialista.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_hospital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/sub_relatorio_perfil_atendimento_classificacao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_laudo_solicitacao_autorizacao_internacao_hospitalar.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_compatibilidade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_relacionamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_boletim_producao_ambulatorial.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_boletim_procedimento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_resumo_exames_executados.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_mapa_acompanhamento_nutricional.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_estado_nutricional_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_estado_nutricional_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_mapa_acompanhamento_busca.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/prenatal/jrxml/relatorio_relacao_gestantes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_boletim_procedimento_fa_geral.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_boletim_procedimento_fa_geral_agrupar_empresa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_boletim_procedimento_agrupar_empresa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_forma_organizacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_cid.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_tabela_cbo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_cid.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_atendimento_cid_notificado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_leito.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_habilitacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_servico.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_habilitacao_cadastro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_modalidade_cadastro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_registro_cadastro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_servico_cadastro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_detalhe_cadastro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_tipo_leito.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_grupo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_rubrica.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_servico_classificacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_sia_sih_cadastro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_sub_grupo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_financiamento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_sia_sih.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_modalidade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_relacao_usuarios_atendidos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_boletim_atendimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_boletim_atendimentos_agrupado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_atendimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_atendimentos_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_documentos.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
//        list.add(new Relatorio( "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_origem.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_registro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
//        list.add(new Relatorio( "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_ocupacao_cadastro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
//        list.add(new Relatorio( "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_ocupacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
//        list.add(new Relatorio( "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_incremento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimento_gru_habitacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimentos_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimentos_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_conferencia_bpa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_controle_remessa_bpa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_conferencia_bpa_i.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_atendimento.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_atendimento_internacao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_atendimento_internacao_assinatura_digital.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_grupo_problema_condicao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_nota_alta.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_controle_infeccao_hospitalar.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_grafico_distribuicao_encaminhamento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_procedimentos.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_relacao_encaminhamento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_atendimento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_atendimento_anamneses.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_evolucoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_anamneses.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_impressao_boletim.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_impressao_ata_atividade_grupo.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/sub_relatorio_impressao_boletim_profissional.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/sub_relatorio_impressao_boletim_procedimento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_impressao_cronograma.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_local_atividade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_atividade_grupo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_procedimento_atividade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_procedimento_atividade_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_tipo_atividade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_atividade_profissional.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_resumo_encaminhamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_resumo_encaminhamentos_tfd.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_distribuicao_mensal_encaminhamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_encaminhamentos_por_especialidade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
//        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_resumo_agendamentos_tfd.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_encaminhamento_tfd.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_encaminhamento_tfd_estado.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_encaminhamento_tfd_estado_interestadual.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_encaminhamento_tfd_estado_pag2.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/enfermagem/jrxml/relatorio_atendimento_enfermagem.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/enfermagem/jrxml/relatorio_cuidados_procedimento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/enfermagem/jrxml/sub_relatorio_impressao_cuidados.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_atendimento_emergencia.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_atendimento_emergencia_evolucao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_atendimento_emergencia_dispensacao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_termo_autorizacao_tratamento_medico.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_ficha_acolhimento.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_uso_medicacao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_relacao_familiar.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_droga_uso.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_estado_mental.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_atividade_terapeutica.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_termo_compromisso.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_part_1.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_part_2.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_part_3.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_ficha_acolhimento_ecomapa.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/operacao/jrxml/relatorio_impressao_ato_operatorio.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_ficha_anestesia.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_ficha_trabalho_parto.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_atendimento_recem_nascido.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_impressao_requisicao_exame.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_DUAS_VIAS));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_plano_enfermagem.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_prontuario.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_prontuario_resumido.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/exame/jrxml/relatorio_impressao_requisicao_exame_laboratorio.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_documento_encaminhamento_protese.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_ficha_clinica_odontologica.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_transferencia_paciente.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/solicitacaomudancaprocedimento/jrxml/relatorio_imprimir_laudo_solicitacao_mudanca_procedimento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/solicitacaoprocedimentoaih/jrxml/relatorio_imprimir_solicitacao_procedimento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_encaminhamento_ceo.jrxml", ICustomizeJasperDesign.CAB_ESTADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/enfermagem/jrxml/relatorio_impressao_prescricao_enfermagem.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/internacao/jrxml/relatorio_relacao_internacao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/atividadegrupo/jrxml/relatorio_relacao_procedimento_atividade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/registroorientacao/jrxml/relatorio_relacao_registro_orientacao_diversas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/registroorientacao/jrxml/relatorio_relacao_registro_orientacao_diversas_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_declaracao_processo_tfd.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_atendimentos_convenio.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autointimacao/jrxml/laudo_medicamentos_especiais.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SUS_DETALHES_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/impressao_notificacao_tuberculose.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_atendimentos_bairro_municipio.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_atendimentos_bairro_municipio_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_exportacao_agenda.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_exportacao_agenda_laboratorio.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_encaminhamento_paciente.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_tempo_medio_atendimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_transferencia_encaminhamento_paciente.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_transferencia_encaminhamento_paciente_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_impressao_atendimento_painel.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/basico/jrxml/relatorio_lista_atendimentos_cancelar.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/jrxml/sub_relatorio_impressao_prontuario_resumido_lst_problemas.jrxml"));

        //não sabia onde colocar * ver com alguem depois
        list.add(new Relatorio("/br/com/ksisolucoes/report/relacaoexame/jrxml/relatorio_relacao_exames.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_relacao_entrada_paciente_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_relacao_entrada_paciente_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> entrada() {

        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_receitas_controladas.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_grafico_cruzamento_dispensacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_grafico_demonstrativo_dispensacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_grafico_demonstrativo_dispensacao_judicial.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_grafico_variacao_dispensacao_barras.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_grafico_variacao_anual_dispensacao_barras.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_grafico_variacao_dispensacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_medicamento_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_receituario.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_unidade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_resumo_dispensacao_paciente.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_extrato_paciente.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_extrato_paciente_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_resumo_dispensacao_judicial.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_medicamento_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_liberacao_receita_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_liberacao_receita_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_listagem_produtos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_impressao_dispensacao_medicamento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_impressao_dispensacao_medicamento_impressora_termica.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_impressao_dispensacao_prescricao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/sub_relatorio_impressao_dispensacao_medicamento.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_termo_abertura_encerramento.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_anexo_xix.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_anexo_encerramento_xix.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_anexo_xviii.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_rmnra.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_paciente_dispecacao_atraso.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_liberacao_receita_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_liberacao_receita_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/recebimento/relatorio/jrxml/relatorio_registro_item_nota_fiscal.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/recebimento/relatorio/jrxml/relatorio_registro_item_nota_fiscal_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/recebimento/relatorio/jrxml/relatorio_recebimento_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_recibo_produto_solicitado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_produto_solicitado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/relatorio_produto_solicitado_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_produto_solicitado_dispensado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/basico/jrxml/sub_relatorio_produto_solicitado_entregas.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/pedido/jrxml/relacao_pedido_transferencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/pedido/jrxml/relacao_pedido_transferencia_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/pedidocompra/jrxml/relatorio_impressao_pedido_compra.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/pedidocompra/jrxml/sub_relatorio_impressao_pedido_compra_itens.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_valorizacao_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/grafico_valorizacao_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_produtos_vencendo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/grafico_produtos_vencendo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_separacao_pedido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_relacao_prescricoes_nao_dispensadas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_relacao_prescricoes_nao_dispensadas_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_produtos_validade_vencida_vencer.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_transferencia_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_relacao_faltas_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/dispensacao/jrxml/relatorio_dispensacao_relacao_faltas_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> vacinas() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_estoque_vacinas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_impressao_pedido_vacina.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_impressao_pedido_insumo.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_pedido_vacina_rotina.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_pedido_insumo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_recebimento_vacinas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_impressao_calendario_vacinacao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_impressao_historico_vacinacao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_relacao_vacinas_aplicadas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_relacao_vacinas_aplicadas_campanha.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_relacao_vacinas_aplicadas_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/cadsus/jrxml/relacao_pacientes_vacinas_atrasadas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_carteira_vacinacao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_registros_temperatura.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_pacientes_grupo_vacinacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_pacientes_grupo_vacinacao_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_certificado_vacinacao_digital.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vacina/jrxml/relatorio_relacao_vacinas_nao_aplicadas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> frota() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/relatorio_detalhamento_gastos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/relatorio_previsao_manutencao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/relacao_diario_bordo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/resumo_diario_bordo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/resumo_viagens.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/tfd/roteiroviagemtfd/jrxml/relacao_impressao_roteiro_viagem.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/tfd/roteiroviagemtfd/jrxml/relacao_impressao_solicitacoes_pendentes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/relacao_viagens.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/sub_relacao_viagens.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/relatorio_declaracao_tfd.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/relacao_faltas_viagens_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/frota/jrxml/relacao_faltas_viagens_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/tfd/roteiroviagemtfd/jrxml/relatorio_comprovante_manutencao_viagem.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/agendamento/tfd/roteiroviagemtfd/jrxml/relatorio_comprovante_manutencao_viagem_lote.jrxml"));

        return list;
    }

    private List<Relatorio> consorcio() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_guia_procedimentos_reduzido.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA));
        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_guia_procedimentos2vias.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_DUAS_VIAS_SEM_RODAPE));
        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_guia_procedimentos.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/movimentacaofinanceira/jrxml/relatorio_saldo_contas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/movimentacaofinanceira/jrxml/relatorio_detalhamento_movimentacoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/movimentacaofinanceira/jrxml/relatorio_extrato_contas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/movimentacaofinanceira/jrxml/relatorio_saldo_orcamentario.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/movimentacaofinanceira/jrxml/relatorio_resumo_movimentacoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_detalhamento_guia_procedimento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_guias_a_pagar_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_guias_a_pagar_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_detalhamento_procedimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_relacao_guias.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_resumo_procedimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pagamento/jrxml/relatorio_relacao_pagamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pagamento/jrxml/relatorio_detalhamento_pagamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pagamento/jrxml/relatorio_detalhamento_pagamentos_com_procedimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pagamento/jrxml/relatorio_recibo_consorcio_itens.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/licitacao/jrxml/relatorio_resumo_pedidos_licitacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/licitacao/jrxml/relatorio_detalhamento_licitacoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/licitacao/jrxml/relatorio_detalhamento_pedidos_licitacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/licitacao/jrxml/impressao_produtos_licitacao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacao/jrxml/relatorio_recibo_entrega_produtos.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacao/jrxml/relatorio_pedidos_transferencia_fornecedor.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacao/jrxml/relatorio_pedidos_transferencia_fornecedor_sem_produtos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacaoentrega/jrxml/relatorio_resumo_entregas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacaoentrega/jrxml/relatorio_detalhamento_entregas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacaoentrega/jrxml/relatorio_detalhamento_entregas_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pedidotransferencialicitacao/jrxml/relatorio_saldo_pedidos_transferencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_guia_procedimentos_a4.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SUS_SEM_RODAPE));
        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_guia_procedimentos_a4_cisamosc.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SUS_SEM_RODAPE));
        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_padrao_guia_procedimento_cisamosc.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SUS_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pagamento/jrxml/relatorio_relacao_pagamentos_bancos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pagamento/jrxml/relatorio_resumo_recibos_municipio.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pagamento/jrxml/relatorio_resumo_recibos_prestador.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/procedimento/jrxml/relatorio_procedimento_prestador.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/pagamento/jrxml/relatorio_relacao_prestadores.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_detalhamento_procedimentos_retrato.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_detalhamento_guia_procedimento_retrato.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/consorcio/consorcioguiaprocedimento/jrxml/relatorio_extrato_prestacao_contas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_guia_procedimentos_a4_cisamrec.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_CISAMREC_SEM_RODAPE));
        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_padrao_guia_procedimento_cisamosc_cisamrec.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_CISAMREC_SEM_RODAPE));
        list.add(new Relatorio("/br/com/celk/report/consorcio/jrxml/impressao_guia_procedimentos_a4_cisamosc_cisamrec.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_CISAMREC_SEM_RODAPE));

        return list;
    }

    private List<Relatorio> controle() {
        List<Relatorio> list = new ArrayList<Relatorio>();
        list.add(new Relatorio("/br/com/ksisolucoes/report/controle/acessotemporeal/jrxml/relatorio_acessos_tempo_real.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> vigilancia() {
        List<Relatorio> list = new ArrayList<Relatorio>();
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_documento_requerimento_alvara_sanitario.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_documento_requerimento_licenca_sanitaria.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_documento_requerimento_comprovante.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_requerimento_vigilancia_comprovante.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA_VIGILANCIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_declaracao_visa_outros.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_declaracao_visa_isencao_taxas.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_declaracao_visa_produtos.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_relacao_visita.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_resumo_agravos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_resumo_visita.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_alvaras.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_requerimento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_agravos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/ficha_investigacao_agravo.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_relacao_atividade_veterinaria.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_resumo_atividade_veterinaria.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_solicitacao_agendada_cva.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_solicitacao_pendente_cva.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/jrxml/relatorio_denuncias.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/jrxml/ficha_cva_animal.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/cva/jrxml/relacao_microchipagem.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/cva/jrxml/termo_responsabilidade.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/cva/jrxml/termo_adocao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/jrxml/termo_denuncia.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autoinfracao/jrxml/comprovante_auto_infracao.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/termoajustamentoconduta/jrxml/comprovante_termo_ajustamento_conduta.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/termoajustamentoconduta/jrxml/sub_rel_descricao_conduta_ajustada.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autoinfracao/jrxml/sub_rel_auto_infracao_providencias.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autointimacao/jrxml/comprovante_auto_intimacao.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autointimacao/jrxml/sub_rel_auto_intimacao_exigencias.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autopenalidade/jrxml/comprovante_auto_penalidade.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autopenalidade/jrxml/sub_rel_auto_penalidade_penalidades.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autopenalidade/jrxml/sub_rel_auto_penalidade_providencias.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/automulta/jrxml/comprovante_auto_multa.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/roteiroinspecao/jrxml/roteiro_inspecao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_carteira_saude.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autopenalidade/jrxml/relatorio_auto_penalidade.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autoinfracao/jrxml/relatorio_auto_infracao.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/autointimacao/jrxml/relatorio_auto_intimacao.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/automulta/jrxml/relatorio_auto_multa.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/processoadministrativo/jrxml/relatorio_processo_administrativo.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_pontos_estrategicos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_armadilha.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_visita_armadilha.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/resumo_visita_armadilha.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/dengue/registrodiarioantivetorial/jrxml/producao_servico_antivetorial.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/dengue/registrodiarioantivetorial/jrxml/sub_rel_especimes_servico_antivetorial.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_autorizacao_exumacao.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_baixa_responsabilidade_tecnica.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_certidao_nada_consta.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_impressao_laudo_vistoria.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_impressao_parecer_projeto_hidrossanitario.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_impressao_parecer_projeto_hidrossanitario_declaratorio.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_requerimento_receita_b.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_requerimento_receita_a.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_requerimento_receita.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_requerimento_livro_vigilancia_termo.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_alvara_inicial.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_vacinacao_extramuro.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_despacho.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_relacao_inspecoes_realizadas_detalhado.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_relacao_inspecoes_realizadas_resumido.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_credenciamento.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_autorizacao_sanitaria.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/impressao_habitese.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/impressao_habitese_declaratorio.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/impressao_deferimento_projeto_hidrossanitario.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/impressao_deferimento_projeto_hidrossanitario_declaratorio.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));

        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/impressao_deferimento_projeto_arquitetonico.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_impressao_parecer_projeto_arquitetonico.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));

        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/sub_relatorio_despacho_item.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_alvara_evento.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_alvara_cadastro_evento.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_baixa_responsabilidade_alvara.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_baixa_responsabilidade_revalidacao_alvara.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_licenca_transporte.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/roteiroinspecao/jrxml/roteiro_inspecao_externo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/financeiro/jrxml/memorando.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA_DIREITA_VIGILANCIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/financeiro/jrxml/relatorio_relacao_financeiro.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_relacao_estabelecimentos_sem_rt.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/parecer_tecnico.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_conformidade_tecnica.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_conformidade_tecnica_vistoria_pba.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/termo_solicitacao_servico.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/declaracao_baixa_estabelecimento.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/declaracao_baixa_veiculo.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_lista_inspecao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_inspecao_sanitaria.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/processoadministrativo/jrxml/impressao_parecer_processo_administrativo.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/processoadministrativo/jrxml/impressao_decisao_processo_administrativo.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/requerimento/jrxml/impressao_parecer_requerimento.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/requerimento/jrxml/impressao_requerimento_solicitacao_juridica.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/profissional/jrxml/impressao_profissional_para_receita.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_sindrome_gripal.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_cadastro_individual_notificacao_sinan.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_SEM_RODAPE));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/financeiro/jrxml/relatorio_relacao_financeiro_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_alvaras_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_relacao_escala_plantao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/tempoatendimento/jrxml/relatorio_tempo_atendimento.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/acoesrealizadas/jrxml/relatorio_acoes_realizadas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/declaratorios/pdf_carimbo_prancha.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_licenca_sanitaria.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/jrxml/questionario_populacao_caes_gatos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/vigilancia/cva/jrxml/relatorio_relacao_populacao_caes_gatos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/dispensa_medio_risco.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));
        list.add(new Relatorio("/br/com/ksisolucoes/report/vigilancia/jrxml/desobrigacao_alvara.jrxml", ICustomizeJasperDesign.CAB_VIGILANCIA_SANITARIA_ALVARA));

        return list;
    }

    private List<Relatorio> estoque() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_demonstrativo_produto.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/sub_reservas.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/sub_relatorio_demonstrativo_produto_grupo_estoque.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_produtos_nao_lancados.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_controle_inventario.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_divergencia_inventario.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_grupo_estoque_fisico_nao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_contagem_semi_cega.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_grupo_estoque_fisico_sim.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_localizacao_estoque_fisico_nao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_produtos_inventario_conferencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_localizacao_estoque_fisico_sim.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_lote_estoque_fisico_sim.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_lote_estoque_fisico_nao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_produto_estoque_fisico_nao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_inventario_produto_estoque_fisico_sim.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/sub_relatorio_balanco_completo_medicamentos.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/sub_relatorio_balanco_aquisicoes_medicamentos.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_livro_registro_especifico.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_bmpo_completo.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_bmpo_aquisicoes.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_anexo_xxi.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_estoque_geral_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_estoque_geral_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_estoque_por_grupo_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_estoque_por_grupo_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relacao_produtos_programa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_analise_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_consumo_produto.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_movimentacao_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_movimentacao_estoque_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_movimentacao_estoque_observacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_perdas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/sub_relatorio_movimentacao_estoque.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_produto.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/sub_relatorio_produto.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_produto_unidade.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_pedido_transferencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_pedidos_enviados_separacao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_embarque_pedido_transferencia.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_estoque_minimo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_giro_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_movimentacao_diaria_por_grupo_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_movimentacao_diaria_geral_resumido_produto.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_movimentacao_diaria_por_grupo_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_movimentacao_diaria_geral_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_movimentacao_diaria_resumido_tipo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_movimentacao_em_determinada_data.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_produto_estoque_excel.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_produto_estoque_empresa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_grupo_produto_estoque_empresa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_localizacao_produto_estoque_empresa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_relacao_produtos_ultimo_movimento_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_consumo_produto_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_consumo_produto_detalhado_quebra.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_consumo_produto_mapa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_consumo_produto_mapa_quebra.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_pedido_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_pedidos_divergencia_entrega.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_pedidos_enviados_separacao_resumido_fa_produto_centro_custo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_pedidos_enviados_separacao_resumido_fa_unidade_destino.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_saldo_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_saldo_estoque_divergente.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_previsao_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_impressao_comprovante_entrega.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_classificacao_estoque.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> hospital() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_relacao_atendimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_ocupacao_hospitalar.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_mapa_dietas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_mapa_leitos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/sub_ocupacao_hospitalar.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_impressao_fechamento_conta_paciente.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_liberacao_leito.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_faturamento_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_faturamento_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_agendamentos_cirurgicos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_extrato_conta_financeira.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_impressao_comprovante_agendamento_cirurgico.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/tiss/jrxml/relatorio_guias_tiss.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/tiss/jrxml/relatorio_guias_tiss_portrait.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/tiss/jrxml/sub_relatorio_guia_outras_despesas.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/tiss/jrxml/sub_relatorio_guia_honorarios.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_impressao_espelho_aih.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_fila_aih.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/hospital/jrxml/relatorio_reserva_leito_aih.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> laboratorio() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/laboratorio/exame/jrxml/relatorio_laudo_exames_laboratoriais.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));

        return list;
    }

    private List<Relatorio> materiais() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_impressao_ordem_compra.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_impressao_ordem_compra_pregao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/sub_relatorio_impressao_ordem_compra_itens.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/sub_relatorio_impressao_ordem_compra_itens_pregao.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_impressao_relacao_ordem_compra.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_impressao_relacao_ordem_compra_pregao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_impressao_resumo_autorizacao_fornecimento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_resumo_ordem_compra.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/ordemcompra/jrxml/relatorio_resumo_ordem_compra_pregao.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_impressao_etiqueta_produto.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_impressao_etiqueta_produto_zebra.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_impressao_etiqueta_produto_elgin_34x34.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_impressao_etiqueta_produto_elgin_33x21.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_impressao_etiqueta_volume.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_impressao_rotulo_produto.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/emprestimo/jrxml/relatorio_relacao_emprestimo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/emprestimo/jrxml/relatorio_resumo_emprestimos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/emprestimo/jrxml/relatorio_comprovante_emprestimo.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA));
        list.add(new Relatorio("/br/com/celk/report/emprestimo/jrxml/relatorio_relacao_devolucoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/emprestimo/jrxml/relatorio_resumo_devolucoes.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/emprestimo/jrxml/relatorio_comprovante_devolucao_emprestimo.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO_A4_PAISAGEM_A5_VIA_UNICA));
        //list.add(new Relatorio("/br/com/celk/report/emprestimo/jrxml/relatorio_comprovante_devolucao_emprestimo_subreport.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_impressao_etiqueta_embarque.jrxml"));
        list.add(new Relatorio("/br/com/ksisolucoes/report/materiais/basico/jrxml/relatorio_impressao_etiqueta_produto_nota_entrada.jrxml"));

        return list;
    }

    private List<Relatorio> patrimonio() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/patrimonio/jrxml/relacao_patrimonios.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> recepcao() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/celk/report/recepcao/jrxml/relatorio_impressao_ficha_atendimento_ambulatorial.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/celk/report/recepcao/jrxml/relatorio_impressao_etiqueta_exame.jrxml"));

        return list;
    }

    private List<Relatorio> indicadores() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/indicadores/jrxml/relatorio_relacao_metas.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/indicadores/jrxml/relatorio_indicadores.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> receita() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/celk/report/medicamentosusocontinuo/jrxml/relatorio_relacao_medicamentos_uso_continuo_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/medicamentosusocontinuo/jrxml/relatorio_relacao_medicamentos_uso_continuo_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    private List<Relatorio> unidadeSaude() {
        List<Relatorio> list = new ArrayList<Relatorio>();

        list.add(new Relatorio("/br/com/ksisolucoes/report/unidadesaude/relatorio/jrxml/relatorio_planilha_diarreia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/unidadesaude/relatorio/jrxml/relatorio_planilha_diarreia_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/unidadesaude/relatorio/jrxml/relatorio_impressao_lancamento_producao.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_visita_domiciliar_acs_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_visita_domiciliar_acs_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_procedimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_atendimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_relacao_pacientes_tratamento_caps.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_relacao_pacientes_atendimento_atraso.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/ksisolucoes/report/domicilio/jrxml/relatorio_impressao_cadastro_ficha_domiciliar.jrxml", ICustomizeJasperDesign.CAB_TIMBRADO));
        list.add(new Relatorio("/br/com/ksisolucoes/report/preventivo/jrxml/relatorio_resultado_preventivo.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/relatorio_relacao_lotes_esus.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/relatorio_relacao_lotes_esus_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/relatorio_relacao_lotes_esus_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_fichas_atend_domiciliar_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_fichas_atend_domiciliar_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/relatorio_condicao_moradia_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/relatorio_condicao_moradia_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/prontuario/jrxml/relatorio_registros_sintomaticos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/prontuario/jrxml/relatorio_acompanhamento_tratamentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/publico/consorcio/jrxml/relatorio_publico_atendimentos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_atendimentos_compartilhados.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/relatorio_programa_mais_medicos.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/sub_relatorio_programa_mais_medicos_producao_equipe.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/sub_relatorio_programa_mais_medicos_territorio_equipe.jrxml"));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_historico_atendimentos_paciente.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_risco_cardiovascular.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_monitoramento_upa.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_monitoramento.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_estratificacao_individual_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_estratificacao_individual_paciente.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_estratificacao_familiar_resumido.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relatorio_estratificacao_familiar_detalhado.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/esus/jrxml/relatorio_cadastro_individual.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relacao_hipertensos_sem_aderencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relacao_diabeticos_sem_aderencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relacao_criancas_sem_aderencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relacao_mulheres_sem_aderencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));
        list.add(new Relatorio("/br/com/celk/report/unidadesaude/jrxml/relacao_gestantes_sem_aderencia.jrxml", ICustomizeJasperDesign.CAB_ROD_SIMPLES));

        return list;
    }

    public List<Relatorio> getCompilaReports() {
        List<Relatorio> lista = new ArrayList<Relatorio>();

        lista.addAll(this.programaSaude());
        lista.addAll(this.prontuario());
        lista.addAll(this.cadSus());
        lista.addAll(this.geral());
        lista.addAll(this.basico());
        lista.addAll(this.agendas());
        lista.addAll(this.entrada());
        lista.addAll(this.estoque());
        lista.addAll(this.vacinas());
        lista.addAll(this.frota());
        lista.addAll(this.consorcio());
        lista.addAll(this.controle());
        lista.addAll(this.vigilancia());
        lista.addAll(this.hospital());
        lista.addAll(this.laboratorio());
        lista.addAll(this.materiais());
        lista.addAll(this.patrimonio());
        lista.addAll(this.recepcao());
        lista.addAll(this.indicadores());
        lista.addAll(this.receita());
        lista.addAll(this.unidadeSaude());

        return lista;
    }

    public static void main(String[] args) {
        new BaseCompilaReports().start(new CompilaReports().getCompilaReports());
    }
}
