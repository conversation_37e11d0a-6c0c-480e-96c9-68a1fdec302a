package br.com.ksisolucoes.bo.appcidadao.ativarappcidadao;

import br.com.ksisolucoes.bo.appcidadao.interfaces.facade.AppCidadaoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.command.ExecutorAsync;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import org.hibernate.criterion.Restrictions;

import java.util.Date;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class CadastrarPacienteAppCidadaoCommand extends AbstractCommandTransaction<CadastrarPacienteAppCidadaoCommand> {

    UsuarioCadsus usuarioCadsus;
    UsuarioCadsusCns usuarioCadsusCns;

    public CadastrarPacienteAppCidadaoCommand(UsuarioCadsus usuarioCadsus, UsuarioCadsusCns usuarioCadsusCns){
        this.usuarioCadsus = usuarioCadsus;
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    private void validateExistence() throws ValidacaoException{
        UsuarioCadsus existentUsuarioCadsus = getExistingPatient(usuarioCadsus.getCpf());
        if (existentUsuarioCadsus != null) {
            usuarioCadsus = existentUsuarioCadsus;
            throw new ValidacaoException("Já existe um paciente com este CPF cadastrado na base");
        }

        if(usuarioCadsusCns != null){
            UsuarioCadsusCns existentUsuarioCadsusCns = getExistingPatientByCns(usuarioCadsusCns.getNumeroCartao());
            if (existentUsuarioCadsusCns != null) {
                usuarioCadsus = existentUsuarioCadsusCns.getUsuarioCadsus();
                throw new ValidacaoException("Esse CNS já existe na base de dados");
            }
        }

        existentUsuarioCadsus = getExistingPatientByName(usuarioCadsus.getNome(),
                usuarioCadsus.getNomeMae(),
                usuarioCadsus.getDataNascimento());
        if (existentUsuarioCadsus != null) {
            usuarioCadsus = existentUsuarioCadsus;
            throw new ValidacaoException("Já existe um paciente com este nome, nome da mãe e data de nascimento");
        }
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        String email = usuarioCadsus.getEmail();
        try{
            validateExistence();
            BOFactory.save(usuarioCadsus);
            if (usuarioCadsusCns != null) {
                BOFactory.save(usuarioCadsusCns);
            }
        }
        finally {
            BOFactory.getBO(AppCidadaoFacade.class).sendEmailPacienteAppCidadao(usuarioCadsus, email);
        }
    }

    private static UsuarioCadsus getExistingPatient(String pacienteCPF) {
        UsuarioCadsus usuarioCadsusProxy = on(UsuarioCadsus.class);

        return LoadManager.getInstance(UsuarioCadsus.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(usuarioCadsusProxy.getCpf()), pacienteCPF))
                .startLeitura()
                .setMaxResults(1)
                .getVO();
    }

    private static UsuarioCadsus getExistingPatientByName(String nome, String nomeMae, Date dataNascimento) {
        UsuarioCadsus usuarioCadsusProxy = on(UsuarioCadsus.class);

        return (UsuarioCadsus) HibernateSessionFactory.getSessionLeitura().createCriteria(UsuarioCadsus.class)
                .add(Restrictions.eq(path(usuarioCadsusProxy.getNome()), nome.toUpperCase()))
                .add(Restrictions.eq(path(usuarioCadsusProxy.getNomeMae()),nomeMae).ignoreCase())
                .add(Restrictions.eq(path(usuarioCadsusProxy.getDataNascimento()), dataNascimento))
                .uniqueResult();
    }

    private static UsuarioCadsusCns getExistingPatientByCns(Long pacientCNS){
        UsuarioCadsusCns usuarioCadsusCnsProxy = on(UsuarioCadsusCns.class);
        return LoadManager.getInstance(UsuarioCadsusCns.class)
                .addProperty(path(usuarioCadsusCnsProxy.getUsuarioCadsus().getNome()))
                .addProperty(path(usuarioCadsusCnsProxy.getUsuarioCadsus().getEmail()))
                .addProperty(path(usuarioCadsusCnsProxy.getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(usuarioCadsusCnsProxy.getNumeroCartao()), pacientCNS))
                .startLeitura()
                .setMaxResults(1)
                .getVO();
    }
}
