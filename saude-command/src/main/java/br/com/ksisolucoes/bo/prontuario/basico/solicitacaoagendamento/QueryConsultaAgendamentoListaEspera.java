package br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryConsultaAgendamentoListaEspera extends CommandQueryPager<QueryConsultaAgendamentoListaEspera> {

    private final AgendamentoListaEsperaDTOParam param;

    private String tipoControleRegulacao;

    public QueryConsultaAgendamentoListaEspera(AgendamentoListaEsperaDTOParam param) {
        this.param = param;
        this.setRealizarCountRegistros(RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    /*
    AVISO:
    ao alterar a consulta para a Tela 777, alterar também a consulta da classe QueryConsultaPacientesPorClassificacaoRisco
     */
    protected void createQuery(HQLHelper hql) throws DAOException {
        getTipoControleRegulacao();
        hql.setTypeSelect(AgendamentoListaEsperaDTO.class.getName());

        hql.addToSelect("solicitacaoAgendamento.codigo", "solicitacaoAgendamento.codigo");
        hql.addToSelect("solicitacaoAgendamento.atendimentoOrigem", "solicitacaoAgendamento.atendimentoOrigem");
        hql.addToSelect("solicitacaoAgendamento", new HQLProperties(SolicitacaoAgendamento.class, "solicitacaoAgendamento").getSingleProperties());

        hql.addToSelect("solicitacaoAgendamentoPosicaoFila.codigo", "solicitacaoAgendamento.solicitacaoAgendamentoPosicaoFila.codigo");
        hql.addToSelect("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera", "solicitacaoAgendamento.solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera");

        hql.addToSelect("usuarioCadsus.codigo", "solicitacaoAgendamento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "solicitacaoAgendamento.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", "solicitacaoAgendamento.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.apelido", "solicitacaoAgendamento.usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.sexo", "solicitacaoAgendamento.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.dataNascimento", "solicitacaoAgendamento.usuarioCadsus.dataNascimento");

        hql.addToSelect("profissional.codigo", "solicitacaoAgendamento.profissional.codigo");
        hql.addToSelect("profissional.referencia", "solicitacaoAgendamento.profissional.referencia");
        hql.addToSelect("profissional.nome", "solicitacaoAgendamento.profissional.nome");

        hql.addToSelect("procedimento.codigo", "solicitacaoAgendamento.procedimento.codigo");
        hql.addToSelect("procedimento.descricao", "solicitacaoAgendamento.procedimento.descricao");

        hql.addToSelect("unidadeOrigem.codigo", "solicitacaoAgendamento.unidadeOrigem.codigo");
        hql.addToSelect("unidadeOrigem.descricao", "solicitacaoAgendamento.unidadeOrigem.descricao");

        hql.addToSelect("tipoProcedimento.codigo", "solicitacaoAgendamento.tipoProcedimento.codigo");
        hql.addToSelect("tipoProcedimento.descricao", "solicitacaoAgendamento.tipoProcedimento.descricao");
        hql.addToSelect("tipoProcedimento.tipoAgendamento", "solicitacaoAgendamento.tipoProcedimento.tipoAgendamento");
        hql.addToSelect("tipoProcedimento.tipoAgenda", "solicitacaoAgendamento.tipoProcedimento.tipoAgenda");
        hql.addToSelect("tipoProcedimento.flagAgendamentoGrupo", "solicitacaoAgendamento.tipoProcedimento.flagAgendamentoGrupo");
        hql.addToSelect("tipoProcedimento.flagValidaConsultaReguladaAnterior", "solicitacaoAgendamento.tipoProcedimento.flagValidaConsultaReguladaAnterior");
        hql.addToSelect("tipoProcedimento.flagCadastrarNaRecepcao", "solicitacaoAgendamento.tipoProcedimento.flagCadastrarNaRecepcao");

        hql.addToSelect("tipoProcedimento.controleCota", "solicitacaoAgendamento.tipoProcedimento.controleCota");

        hql.addToSelect("empresa.codigo", "solicitacaoAgendamento.empresa.codigo");
        hql.addToSelect("empresa.descricao", "solicitacaoAgendamento.empresa.descricao");

        hql.addToSelect("classificacaoRisco.codigo", "solicitacaoAgendamento.classificacaoRisco.codigo");
        hql.addToSelect("classificacaoRisco.descricao", "solicitacaoAgendamento.classificacaoRisco.descricao");
        hql.addToSelect("classificacaoRisco.nivelGravidade", "solicitacaoAgendamento.classificacaoRisco.nivelGravidade");

        hql.addToSelect("empresaPrestador.codigo", "solicitacaoAgendamento.examePrestadorCompetencia.empresa.codigo");
        hql.addToSelect("empresaPrestador.descricao", "solicitacaoAgendamento.examePrestadorCompetencia.empresa.descricao");

        hql.addToSelect("cid.codigo", "solicitacaoAgendamento.cid.codigo");
        hql.addToSelect("cid.descricao", "solicitacaoAgendamento.cid.descricao");

        hql.addToSelect("(SELECT cast(min(t1.numeroCartao) as string) FROM UsuarioCadsusCns t1 WHERE t1.usuarioCadsus.codigo = usuarioCadsus.codigo AND t1.excluido = 0)", "cns");

        StringBuilder from = new StringBuilder();

        if (!RepositoryComponentDefault.SIM_LONG.equals(param.getApenasReagendamento())) {
            from.append("SolicitacaoAgendamentoPendente sap ");
            from.append(" join sap.solicitacaoAgendamento solicitacaoAgendamento ");
        } else {
            from.append("SolicitacaoAgendamento solicitacaoAgendamento ");
        }
        from.append(" left join solicitacaoAgendamento.solicitacaoAgendamentoPosicaoFila solicitacaoAgendamentoPosicaoFila "
                + " join solicitacaoAgendamento.tipoProcedimento tipoProcedimento "
                + " join solicitacaoAgendamento.procedimento procedimento "
                + " join solicitacaoAgendamento.empresa empresa "
                + " join solicitacaoAgendamento.usuarioCadsus usuarioCadsus "
                + " left join solicitacaoAgendamento.examePrestadorCompetencia examePrestadorCompetencia "
                + " left join solicitacaoAgendamento.unidadeOrigem unidadeOrigem "
                + " left join solicitacaoAgendamento.classificacaoRisco classificacaoRisco "
                + " left join solicitacaoAgendamento.subclassificacaoFilaEspera subclassificacaoFilaEspera"
                + " left join examePrestadorCompetencia.empresa empresaPrestador "
                + " left join solicitacaoAgendamento.profissional profissional "
                + " left join solicitacaoAgendamento.profissionalDesejado profissionalDesejado "
                + " left join solicitacaoAgendamento.cid cid "
                + ", DominioUsuarioCadsus dom ");

        hql.addToFrom(from.toString());

        hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", RepositoryComponentDefault.NAO);
        hql.addToWhereWhithAnd("dom.usuarioCadsus.codigo = usuarioCadsus.codigo");
        hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoFila = ", Coalesce.asLong(param.getTipoFila(), SolicitacaoAgendamento.TIPO_FILA_NORMAL));

        if (param.getTipoProcedimento() != null && param.getTipoProcedimento().getCodigo() != null) {
            if (!RepositoryComponentDefault.SIM_LONG.equals(param.getApenasReagendamento())) {
                hql.addToWhereWhithAnd("sap.tipoProcedimento.codigo = ", param.getTipoProcedimento().getCodigo());
            } else {
                hql.addToWhereWhithAnd("tipoProcedimento.codigo = ", param.getTipoProcedimento().getCodigo());
            }
        }

        if (param.getProcedimento() != null && param.getProcedimento().getCodigo() != null) {
            hql.addToWhereWhithAnd("procedimento.codigo = ", param.getProcedimento().getCodigo());
        }
        if (param.getOrigemSolicitacao() != null && param.getOrigemSolicitacao().getCodigo() != null) {
            hql.addToWhereWhithAnd("unidadeOrigem.codigo = ", param.getOrigemSolicitacao().getCodigo());
        }
        if (param.getCodigoPaciente() != null) {
            hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getCodigoPaciente());
        }
        if (param.getProfissionalDesejado() != null) {
            hql.addToWhereWhithAnd("profissionalDesejado =", param.getProfissionalDesejado());
        }
        if (param.getCns() != null) {
            hql.addToWhereWhithAnd("(SELECT min(t1.numeroCartao) FROM UsuarioCadsusCns t1 WHERE t1.usuarioCadsus.codigo = usuarioCadsus.codigo AND t1.excluido = 0) = ", param.getCns());
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(param.getApenasBloqueada())){
            hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado = ", RepositoryComponentDefault.SIM_LONG);
        }
        if (param.getCodigoSolicitacao() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.codigo = ", new Long(param.getCodigoSolicitacao()));
        }
        if (param.getPaciente() != null) {
            hql.addToWhereWhithAnd(hql.getConsultaLikedIgnoreNothing(" dom.nomeReferencia", param.getPaciente()));
        }
        if (param.getNumeracaoAuxiliar() != null) {
            hql.addToWhereWhithAnd(hql.getConsultaLiked("solicitacaoAgendamento.numeracaoAuxiliar ", param.getNumeracaoAuxiliar()));
        }
        if (param.getDataNascimento() != null) {
            hql.addToWhereWhithAnd("usuarioCadsus.dataNascimento = ", param.getDataNascimento());
        }
        if (param.getTipoConsulta() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoConsulta in ", param.getTipoConsulta());
        }
        if (param.getTipoEstabelecimento() != null) {
            hql.addToWhereWhithAnd("empresa.tipoUnidade = ", param.getTipoEstabelecimento());
        }
        if (param.getPeriodo() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.dataSolicitacao", param.getPeriodo());
        }
        if (param.getClassificacaoRisco() != null && param.getClassificacaoRisco().getCodigo() != null) {
            hql.addToWhereWhithAnd("classificacaoRisco.codigo = ", param.getClassificacaoRisco().getCodigo());
        }
        if (param.getSubClassificacao() != null && param.getSubClassificacao().getCodigo() != null) {
            hql.addToWhereWhithAnd("subclassificacaoFilaEspera.codigo = ", param.getSubClassificacao().getCodigo());
        }

        if (param.getExameProcedimento() != null) {
            hql.addToWhereWhithAnd("exists(" +
                    " select 1 " +
                    " from SolicitacaoAgendamentoExame sae " +
                    " WHERE sae.solicitacaoAgendamento.codigo = solicitacaoAgendamento.codigo " +
                    " AND sae.exameProcedimento.codigo = " + param.getExameProcedimento().getCodigo() + ") ");
        }

        //Conforme SolicitacaoAgendamento.getSolicitarPrioridadeFormatadoData();
        if (param.getPrioridade() != null) {
            if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_TIPO_PROCEDIMENTO.equals(tipoControleRegulacao)) {

                hql.addToWhereWhithAnd("(solicitacaoAgendamento.solicitarPrioridade = " + RepositoryComponentDefault.NAO_LONG
                        + " or solicitacaoAgendamento.flagAvaliacaoAprovado = " + RepositoryComponentDefault.SIM_LONG + ")");

                hql.addToWhereWhithAnd("solicitacaoAgendamento.flagDevolvido <> ", RepositoryComponentDefault.SIM_LONG);
                hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado <> ", RepositoryComponentDefault.SIM_LONG);
                hql.addToWhereWhithAnd("solicitacaoAgendamento.prioridade = ", param.getPrioridade());

            } else if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(tipoControleRegulacao)) {
                if (SolicitacaoAgendamento.PRIORIDADE_BREVIDADE.equals(param.getPrioridade())) { //ATENDIDA
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagAvaliacaoAprovado <> ", RepositoryComponentDefault.NAO_LONG);
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.prioridade = ", param.getPrioridade());
                } else if (SolicitacaoAgendamento.STATUS_BLOQUEADO.equals(param.getPrioridade())) { //BLOQUEADA
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado = ", RepositoryComponentDefault.SIM_LONG);
                } else if (SolicitacaoAgendamento.STATUS_DEVOLVIDO.equals(param.getPrioridade())) { //DEVOLVIDA
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagDevolvido =", RepositoryComponentDefault.SIM_LONG);
                } else if (SolicitacaoAgendamento.STATUS_AGENDADO.equals(param.getPrioridade())) { //SOLICITADA
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.solicitarPrioridade <> ", RepositoryComponentDefault.NAO_LONG);
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagAvaliacaoAprovado <> ", RepositoryComponentDefault.SIM_LONG);
                    hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado <> ", RepositoryComponentDefault.SIM_LONG);
                }
            }
        }

        if (param.isPrestador()) {
            if(param.getEmpresas() != null) {
                hql.addToWhereWhithAnd("empresaPrestador in ", param.getEmpresas());
            } else if (!param.isPermissaoEmpresa()){
                hql.addToWhereWhithAnd("empresaPrestador in ", param.getLstEmpresasUsuario());
            }
        } else {
            if(param.getEmpresas() != null) {
                hql.addToWhereWhithAnd("empresa in ", param.getEmpresas());
            } else if (!param.isPermissaoEmpresa()){
                hql.addToWhereWhithAnd("empresa in ", param.getLstEmpresasUsuario());
            }
        }

        if (!param.isPermissaoTipoProcedimento() && param.getInCodigosEmpresasUsuario() != null && !param.getInCodigosEmpresasUsuario().isEmpty()) {
            StringBuilder sb = new StringBuilder();
            sb.append("exists(select 1 from TipoProcedimentoAgenda tipoProcAgend left join tipoProcAgend.tipoProcedimento tipoProc left join tipoProcAgend.empresa emp ");
            sb.append("where tipoProc.codigo = tipoProcedimento.codigo ");
            sb.append("and emp.codigo in ");
            sb.append(param.getInCodigosEmpresasUsuario());
            sb.append(")");
            hql.addToWhereWhithAnd(sb.toString());
        }

        if (!param.isPermissaoVisualizarTipoProcAgend() && param.getInCodigosEmpresasUsuario() != null && !param.getInCodigosEmpresasUsuario().isEmpty()) {
            StringBuilder sb = new StringBuilder();
            sb.append("exists(select 1 from TipoProcedimentoEmpresaAgendar tipoProcEmpAgend left join tipoProcEmpAgend.tipoProcedimento tipoProc2 left join tipoProcEmpAgend.empresa emp2 ");
            sb.append("where tipoProc2.codigo = tipoProcedimento.codigo ");
            sb.append("and emp2.codigo in ");
            sb.append(param.getInCodigosEmpresasUsuario());
            sb.append(")");
            hql.addToWhereWhithAnd(sb.toString());
        }

        if (!param.isPermissaoVisualizarTipoProcAgend() && param.getUsuarioLogado() != null) {
            StringBuilder sb = new StringBuilder();
            sb.append("exists(select 1 from TipoProcedimentoUsuarioAgendar tipoProcUsuAgend left join tipoProcUsuAgend.tipoProcedimento tipoProc2 left join tipoProcUsuAgend.usuario usu ");
            sb.append("where tipoProc2.codigo = tipoProcedimento.codigo ");
            sb.append("and usu.codigo = ");
            sb.append(param.getUsuarioLogado().getCodigo());
            sb.append(")");
            hql.addToWhereWhithAnd(sb.toString());
        }

        if (param.getApenasBloqueada() == null || RepositoryComponentDefault.NAO_LONG.equals(param.getApenasBloqueada())) {
            if (RepositoryComponentDefault.SIM_LONG.equals(param.getApenasReagendamento())) {
                hql.addToWhereWhithAnd("tipoProcedimento.flagReagendamento = ", RepositoryComponentDefault.SIM_LONG);
                hql.addToWhereWhithAnd("solicitacaoAgendamento.status in (:statusList)");
            } else {
                hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", param.getSituacaoList());
            }
        }

        String ordenarFaixaEtaria = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("OrdenarFaixaEtaria");
        FaixaEtaria faixaEtaria = null;
        if (Coalesce.asString(ordenarFaixaEtaria, RepositoryComponentDefault.NAO).equals(RepositoryComponentDefault.SIM)) {
            faixaEtaria = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("FaixaEtaria");
            if (faixaEtaria != null) {
                hql.addToFrom("FaixaEtariaItem faixaEtariaItem");
                hql.addToWhereWhithAnd("faixaEtariaItem.id.faixaEtaria = ", faixaEtaria);
                hql.addToWhereWhithAnd("extract(year from age(usuarioCadsus.dataNascimento)) * 12 + extract(month from age(usuarioCadsus.dataNascimento)) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal");
            }
        }

        String orderType = Coalesce.asString(param.getTipoOrdenacao(), "asc");
        String orderField = param.getCampoOrdenacao();

        if (StringUtils.trimToNull(param.getCampoOrdenacao()) != null) {
            hql.addToOrder(orderField + " " + orderType);
        }

        if (param.getConfigureParam().getSorter() != null) {
            for (Map.Entry<String, String> entry : param.getConfigureParam().getSorter().entrySet())
                hql.addToOrder(entry.getKey() + " " + entry.getValue());
        }

        if (param.isTipoFilaRegulacao()) {
            hql.addToOrder("solicitacaoAgendamento.classificacaoRisco.nivelGravidade asc");
            hql.addToOrder("solicitacaoAgendamento.valorSubclassificacaoFilaEspera asc");
            hql.addToOrder("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera asc");
            hql.addToOrder("solicitacaoAgendamento.dataSolicitacao asc");
            hql.addToOrder("solicitacaoAgendamento.codigo asc");
        } else {
            hql.addToOrder("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera");
            hql.addToOrder("solicitacaoAgendamento.codigo");
        }

        param.setParametroGem(tipoControleRegulacao);
    }

    public void getTipoControleRegulacao() {
        if (tipoControleRegulacao == null) {
            try {
                tipoControleRegulacao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void setParameters(Query query) {
        if (RepositoryComponentDefault.SIM_LONG.equals(param.getApenasReagendamento())) {
            query.setParameterList("statusList", Arrays.asList(SolicitacaoAgendamento.STATUS_AGENDADO, SolicitacaoAgendamento.STATUS_AGENDADO_FORA_REDE));
        }
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if(CollectionUtils.isNotNullEmpty(list)){
            AgendamentoListaEsperaDTO dto;
            SolicitacaoAgendamentoExame solicitacaoAgendamentoExame;
            SolicitacaoAgendamentoExame proxy = on(SolicitacaoAgendamentoExame.class);
            for(Object item : list){
                dto = (AgendamentoListaEsperaDTO) item;

                solicitacaoAgendamentoExame = LoadManager.getInstance(SolicitacaoAgendamentoExame.class)
                        .addProperty(path(proxy.getExameProcedimento().getDescricaoProcedimento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento().getCodigo()), dto.getSolicitacaoAgendamento().getCodigo()))
                        .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo()), BuilderQueryCustom.QuerySorter.CRESCENTE))
                        .setMaxResults(1)
                        .start().getVO();

                if(solicitacaoAgendamentoExame != null){
                    dto.setDescricaoExameProcedimento(solicitacaoAgendamentoExame.getExameProcedimento().getDescricaoProcedimento());
                }
            }
        }
    }

}
