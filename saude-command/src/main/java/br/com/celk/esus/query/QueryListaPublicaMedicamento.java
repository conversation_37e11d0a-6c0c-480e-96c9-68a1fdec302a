package br.com.celk.esus.query;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.ConsultaMedicamentoPublicoDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.ConsultaMedicamentoPublicoDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.AliasToBeanNestedResultTransformer;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.type.DoubleType;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;

import java.util.List;

/**
 * Created by murilo.
 */
public class QueryListaPublicaMedicamento extends CommandQueryPager<QueryListaPublicaMedicamento> {

    private ConsultaMedicamentoPublicoDTOParam param;

    private List<ConsultaMedicamentoPublicoDTO> result;

    public QueryListaPublicaMedicamento(ConsultaMedicamentoPublicoDTOParam param) {
        this.param = param;
        this.setRealizarCountListaPublica(RepositoryComponentDefault.SIM_LONG);
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(ConsultaMedicamentoPublicoDTO.class.getName());
        hql.setUseSQL(true);

        hql.addToSelect("produto.cod_pro", "codigo");
        hql.addToSelect("produto.referencia", "referencia");
        hql.addToSelect("subg.controlado", "flagControlado");
        hql.addToSelect("subg.flag_judicial", "flagJudicial");
        hql.addToSelect("subg.flag_portaria_344", "flagPortaria344");
        hql.addToSelect("produto.descricao", "descricao");
        hql.addToSelect("unidade.unidade", "unidade");
        hql.addToSelect("estoqueEmpresa.saldoDisponivel", "saldoDisponivel");
        hql.addToFrom("produtos produto "+
                " join subgrupo subg on subg.cod_sub = produto.cod_sub and produto.cod_gru = subg.cod_gru "+
                " join unidade unidade on		unidade.cod_uni = produto.cod_uni "+
                " join (select "+
                "        estoque_empresa.cod_pro, "+
                "        (sum(case when grupoEstoque.dt_validade >= current_date then coalesce(grupoEstoque.estoque_fisico, 0) else 0 end) - sum(case when grupoEstoque.dt_validade >= current_date then coalesce(grupoEstoque.estoque_reservado , 0) else 0 end)) as saldoDisponivel "+
                "        from "+
                "                estoque_empresa "+
                "        join empresa emp on	estoque_empresa.empresa = emp.empresa "+
                "        left join grupo_estoque grupoEstoque on		grupoEstoque.empresa = estoque_empresa.empresa		and grupoEstoque.cod_pro = estoque_empresa.cod_pro "+
                "        where "+
                "       estoque_empresa.flag_ativo = :valorAtivo "+
                "        and (estoque_empresa.estoque_fisico - estoque_empresa.estoque_reservado) >= :valorEstoque "+
                "        and emp.flag_lista_medicamento_publico = :flagListaMedicamentoPublicoSim "+
                "        group by "+
                "        estoque_empresa.cod_pro "+
                "  )  as estoqueEmpresa on estoqueEmpresa.cod_pro =  produto.cod_pro");

        hql.addToWhereWhithAnd("((coalesce(subg.flag_lista_medicamento_publico, :flagListaMedicamentoPublicoNao) = :flagListaMedicamentoPublicoSim and coalesce(produto.flag_lista_medicamento_publico, :flagListaMedicamentoPublicoSim) = :flagListaMedicamentoPublicoSim)"
                + " or"
                + " (coalesce(subg.flag_lista_medicamento_publico, :flagListaMedicamentoPublicoNao) = :flagListaMedicamentoPublicoNao and coalesce(produto.flag_lista_medicamento_publico, :flagListaMedicamentoPublicoNao) = :flagListaMedicamentoPublicoSim))");
        if (param.getDescricao() != null) {
            hql.addToWhereWhithAnd(hql.getConsultaLiked("produto.descricao ", param.getDescricao()));
        }
        hql.addToOrder("produto.descricao asc");
        hql.addToOrder("saldoDisponivel desc");
    }

    @Override
    protected void setParameters(Query query) {
        super.setParameters(query);
        query.setParameter("valorEstoque", 0L);
        query.setParameter("valorAtivo", RepositoryComponentDefault.SIM);
        query.setParameter("flagListaMedicamentoPublicoSim", RepositoryComponentDefault.SIM_LONG);
        query.setParameter("flagListaMedicamentoPublicoNao", RepositoryComponentDefault.NAO_LONG);
    }

    @Override
    protected Object executeQuery(Query query) {
        SQLQuery sql = (SQLQuery) query;
        sql.addScalar("codigo", StringType.INSTANCE)
                .addScalar( "referencia", StringType.INSTANCE)
                .addScalar( "flagControlado", StringType.INSTANCE)
                .addScalar( "flagJudicial", LongType.INSTANCE)
                .addScalar( "flagPortaria344", LongType.INSTANCE)
                .addScalar( "descricao", StringType.INSTANCE)
                .addScalar( "unidade", StringType.INSTANCE)
                .addScalar( "saldoDisponivel", DoubleType.INSTANCE)

                .setResultTransformer(new AliasToBeanNestedResultTransformer(ConsultaMedicamentoPublicoDTO.class, super.getHQL().getPropBindingList()));

        result = sql.list();
        return result;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (ConsultaMedicamentoPublicoDTO dto : result) {
            StringBuilder classificacaoBuilder = new StringBuilder();

            if (dto.getFlagControlado().equals(RepositoryComponentDefault.SIM)) {
                classificacaoBuilder.append("Controlado");
                classificacaoBuilder.append("/");
            }
            if (dto.getFlagJudicial().equals(RepositoryComponentDefault.SIM_LONG)) {
                classificacaoBuilder.append("Judicial");
                classificacaoBuilder.append("/");
            }
            if (dto.getFlagPortaria344().equals(RepositoryComponentDefault.SIM_LONG)) {
                classificacaoBuilder.append("Portaria 344");
                classificacaoBuilder.append("/");
            }

            String classificacao = classificacaoBuilder.toString();
            if (classificacao.endsWith("/")) {
                classificacao = classificacao.substring(0, classificacao.length() - 1);
            }

            dto.setClassificacao(classificacao.isEmpty() ? " " : classificacao);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = (List<ConsultaMedicamentoPublicoDTO>) result;
    }
}