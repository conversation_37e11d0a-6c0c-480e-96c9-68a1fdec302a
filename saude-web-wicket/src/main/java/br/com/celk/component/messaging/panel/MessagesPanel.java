package br.com.celk.component.messaging.panel;

import br.com.celk.component.messaging.MensagensNovaCache;
import br.com.celk.component.repeatingview.ReverseRepeatingView;
import br.com.celk.system.session.ApplicationSession;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MessagesPanel extends Panel {

    private ReverseRepeatingView repeatingView;
    private Map<Long, IMessagePanel> messagePanels = new HashMap<Long, IMessagePanel>();

    public MessagesPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        add(repeatingView = new ReverseRepeatingView("repeaterMensagens"));
        List<QueryConsultaMensagensDTO> dtoList = MensagensNovaCache.get();

        for (QueryConsultaMensagensDTO dto : dtoList) {
            IMessagePanel messagePanel = new MessagePanel(repeatingView.newChildId(), dto);
            messagePanels.put(dto.getMensagem().getCodigo(), messagePanel);
            repeatingView.add((Component) messagePanel);
        }
    }

    public int getMessageCount() {
        return repeatingView.size();
    }

    public void notifyMessage(AjaxRequestTarget target, QueryConsultaMensagensDTO dto) {
        IMessagePanel messagePanel = messagePanels.get(dto.getMensagem().getCodigo());
        if (RepositoryComponentDefault.SimNaoLong.NAO.value().equals(dto.getMensagem().getLida()) && RepositoryComponentDefault.SimNaoLong.NAO.value().equals(dto.getMensagem().getExcluida())) {
            if (!ApplicationSession.get().getMensagensNovasCache().contains(dto.getMensagem())) {
                ApplicationSession.get().getMensagensNovasCache().add(dto.getMensagem());
            }
            if (messagePanel != null) {
                messagePanel.notifyMessage(target, dto.getMensagem());
            } else {
                messagePanel = new MessagePanel(repeatingView.newChildId(), dto);
                messagePanels.put(dto.getMensagem().getCodigo(), messagePanel);
                repeatingView.add((Component) messagePanel);
                target.add(this);
            }
        } else if (messagePanel != null) {
            messagePanels.remove(dto.getMensagem().getCodigo());
            repeatingView.remove((Component) messagePanel);
            target.add(this);

            if (ApplicationSession.get().getMensagensNovasCache().contains(dto.getMensagem())) {
                ApplicationSession.get().getMensagensNovasCache().remove(dto.getMensagem());
            }
        }
    }
}
