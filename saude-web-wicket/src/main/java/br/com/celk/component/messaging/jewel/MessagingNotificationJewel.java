package br.com.celk.component.messaging.jewel;

import br.com.celk.component.lazypanel.LazyPanel;
import br.com.celk.component.messaging.panel.MessagesPanel;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.comunicacao.mensagem.CaixaMensagensPage;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public class MessagingNotificationJewel extends Panel{

    private Label lblCount;
    private MessagesPanel messagesPanel;
    
    public MessagingNotificationJewel(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        add(lblCount = new Label("lblCount", new LoadableDetachableModel() {

            @Override
            protected Object load() {
                return ApplicationSession.get().getQuantidadeMensagensUsuario();
            }
        }));
        lblCount.setOutputMarkupId(true);
        lblCount.setOutputMarkupPlaceholderTag(true);
        add(new LazyPanel("ajaxLazyLoad") {

            @Override
            public Component getLazyLoadComponent(String markupId, AjaxRequestTarget target) {
                messagesPanel = new MessagesPanel(markupId);
                lblCount.setVisible(messagesPanel.getMessageCount()!=0);
                target.add(lblCount);
                return messagesPanel;
            }
        });

        lblCount.setVisible(false);
        
        add(new BookmarkablePageLink("linkEntrada", CaixaMensagensPage.class));
    }

    public void notifyMessage(AjaxRequestTarget target, QueryConsultaMensagensDTO dto) {
        if (messagesPanel!=null) {
            messagesPanel.notifyMessage(target, dto);
        }
        lblCount.setVisible(messagesPanel.getMessageCount()!=0);
        target.add(lblCount);
    }

}
