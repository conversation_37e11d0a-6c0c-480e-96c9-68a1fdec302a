package br.com.celk.view.vacina.vacinaaplicacao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.faixaetaria.autocomplete.AutoCompleteConsultaFaixaEtaria;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaTipoVacina;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.vacina.dto.RelatorioRelacaoVacinasAplicadasDTOParam;
import br.com.ksisolucoes.report.vacina.interfaces.facade.VacinaReportFacade;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vacina.Calendario;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class RelatorioRelacaoVacinasAplicadasPage extends RelatorioPage<RelatorioRelacaoVacinasAplicadasDTOParam> {

    private AutoCompleteConsultaEmpresa acUnidade;
    private AutoCompleteConsultaTipoVacina acTipoVacina;
    private AutoCompleteConsultaUsuarioCadsus acUsuarioCadsus;
    private AutoCompleteConsultaUsuario acUsuario;
    private AutoCompleteConsultaFaixaEtaria acFaixaEtaria;

    private DropDown<FaixaEtariaItem> ddFaixaEtariaItem;
    private DropDown ddGrupoAtendimento;
    private DropDown ddEstrategia;
    private DropDown ddHistorico;
    private DropDown<EquipeArea> ddArea;
    private DropDown<EquipeMicroArea> ddMicroArea;
    private RequiredPnlDatePeriod periodo;

    private DropDown ddFormaApresentacao;
    private DropDown<RelatorioRelacaoVacinasAplicadasDTOParam.Ordenacao> ddOrdenacao;

    private CheckBoxLongValue checkExibirGruposVazios;
    private Label lbExibirGruposVazios;

    private DropDown<RelatorioRelacaoVacinasAplicadasDTOParam.TipoRelatorio> ddTipoRelatorio;
    private DropDown ddExibirDoses;
    private DropDown ddDataNascimento;
    private DropDown ddLoteVacina;
    private DropDown ddDataValidadeVacina;
    private DropDown ddFabricanteVacina;
    private DropDown tipoArquivo;
    private DropDown ddSomenteEventosAdversos;
    private DropDown<Long> ddDose;

    private RequiredDateChooser dataInicial;
    private RequiredDateChooser dataFinal;
    private HoraMinutoField horaInicial;
    private HoraMinutoField horaFinal;

    @Override
    public void init(final Form form) {
        RelatorioRelacaoVacinasAplicadasDTOParam proxy = on(RelatorioRelacaoVacinasAplicadasDTOParam.class);

        acUnidade = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa()));
        acUnidade.setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().getUsuario(), Permissions.EMPRESA));
        acUnidade.setLabel(new Model(bundle("unidade")));
        acUnidade.setRequired(false);
        acUnidade.removeRequiredClass();

        acTipoVacina = new AutoCompleteConsultaTipoVacina(path(proxy.getTipoVacina()));
        acUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus()));
        acUsuario = new AutoCompleteConsultaUsuario(path(proxy.getUsuario()));
        acUsuario.setFlagVinculoProfissional(true);

        acFaixaEtaria = new AutoCompleteConsultaFaixaEtaria(path(proxy.getFaixaEtaria()), true);
        acFaixaEtaria.add(new ConsultaListener<FaixaEtaria>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, FaixaEtaria faixaEtaria) {
                updateDropDownFaixaEtariaItem(target, faixaEtaria);
            }
        });
        acFaixaEtaria.add(new RemoveListener<FaixaEtaria>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, FaixaEtaria object) {
                updateDropDownFaixaEtariaItem(target, null);
            }
        });

        ddFaixaEtariaItem = new DropDown<>(path(proxy.getFaixaEtariaItem()));

        createDDEstrategia(proxy);

        ddHistorico = DropDownUtil.getNaoSimLongDropDown(String.valueOf(path(proxy.getHistorico())), false, false);
        ddHistorico.addChoice(null, BundleManager.getString("ambos"));

        createDDArea(proxy);

        ddMicroArea = new DropDown<EquipeMicroArea>(path(proxy.getEquipeMicroArea()));
        ddMicroArea.addChoice(null, BundleManager.getString("todas"));

        dataInicial = new RequiredDateChooser("dataInicial");
        dataFinal   = new RequiredDateChooser("dataFinal");
        horaInicial = new HoraMinutoField("horaInicial");
        horaFinal   = new HoraMinutoField("horaFinal");

        criarDDFormaApresentacao(proxy);

        ddOrdenacao = DropDownUtil.getEnumDropDown(path(proxy.getOrdenacao()), RelatorioRelacaoVacinasAplicadasDTOParam.Ordenacao.values());
        ddTipoRelatorio = DropDownUtil.getEnumDropDown(path(proxy.getTipoRelatorio()), RelatorioRelacaoVacinasAplicadasDTOParam.TipoRelatorio.values());
        checkExibirGruposVazios = new CheckBoxLongValue(path(proxy.getExibirGruposVazios()));
        checkExibirGruposVazios.setOutputMarkupId(true);
        checkExibirGruposVazios.setOutputMarkupPlaceholderTag(true);
        checkExibirGruposVazios.setEnabled(false);
        checkExibirGruposVazios.setVisible(true);

        ddDose = DropDownUtil.getIEnumDropDown(path(proxy.getDose()), VacinaCalendario.Doses.values());

        lbExibirGruposVazios = new Label("lbExibirGruposVazios", new Model<>(bundle("exibirGruposVazios")));
        lbExibirGruposVazios.add(new Tooltip().setText("mensagemCampoImprimirGruposVazios"));

        criarDDTipoRelatorio(proxy);

        ddDataNascimento = DropDownUtil.getSimNaoLongDropDown(path(proxy.getDataNascimento()));
        ddDataNascimento.setEnabled(false);

        ddLoteVacina = DropDownUtil.getSimNaoLongDropDown(path(proxy.getLoteVacina()));
        ddLoteVacina.setEnabled(false);

        ddDataValidadeVacina = DropDownUtil.getSimNaoLongDropDown(path(proxy.getDataValidadeVacina()));
        ddDataValidadeVacina.setEnabled(false);

        ddFabricanteVacina = DropDownUtil.getSimNaoLongDropDown(path(proxy.getFabricanteVacina()));
        ddFabricanteVacina.setEnabled(false);

        ddExibirDoses = DropDownUtil.getSimNaoLongDropDown(path(proxy.getExibirDoses()));
        ddExibirDoses.setEnabled(false);

        ddGrupoAtendimento = (DropDown) DropDownUtil.getDropDownGrupoAtendimentoVacinacao(path(proxy.getGrupoAtendimento()),true).setLabel(new Model(bundle("grupoDeAtendimento")));

        tipoArquivo = DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo");

        ddSomenteEventosAdversos = DropDownUtil.getNaoSimLongDropDown(path(proxy.getSomenteComEventosAdversos()));

        AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo = new AutoCompleteConsultaTabelaCbo(path(proxy.getTabelaCbo()));

        form.add(acUnidade, acTipoVacina, acUsuarioCadsus, acUsuario, acFaixaEtaria, ddFaixaEtariaItem, ddGrupoAtendimento,
            ddEstrategia, ddHistorico, ddArea, ddMicroArea, ddFormaApresentacao, ddOrdenacao, checkExibirGruposVazios,
            lbExibirGruposVazios, ddTipoRelatorio, ddExibirDoses, ddDataNascimento, ddLoteVacina, ddDataValidadeVacina, ddFabricanteVacina,
            tipoArquivo, ddSomenteEventosAdversos, autoCompleteConsultaTabelaCbo, dataInicial, dataFinal, horaInicial, horaFinal, ddDose
        );

        FaixaEtaria faixaEtaria = LoadManager.getInstance(FaixaEtaria.class).setId(FaixaEtaria.FAIXA_ETARIA_CAMPANHA).start().getVO();
        acFaixaEtaria.setComponentValue(faixaEtaria);
        updateDropDownFaixaEtariaItem(null, faixaEtaria);
    }

    @Override
    public void gerarRelatorio(AjaxRequestTarget target) throws IOException, ReportException, ValidacaoException {
        super.gerarRelatorio(target);
    }

    private void updateCheckExibirGruposVazios(AjaxRequestTarget target) {
        if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.CAMPANHA.equals(ddFormaApresentacao.getComponentValue())
                || RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(ddFormaApresentacao.getComponentValue())) {
            checkExibirGruposVazios.setEnabled(true);
        } else {
            checkExibirGruposVazios.setEnabled(false);
            checkExibirGruposVazios.setComponentValue(null);
        }
        target.add(checkExibirGruposVazios);
    }

    private void updateDropDownFaixaEtariaItem(AjaxRequestTarget target, FaixaEtaria faixaEtaria) {
        ddFaixaEtariaItem.removeAllChoices();
        ddFaixaEtariaItem.setEnabled(faixaEtaria != null);

        if (faixaEtaria != null) {
            ddFaixaEtariaItem.addChoice(null, BundleManager.getString("todas"));

            List<FaixaEtariaItem> faixaEtariaItensList = LoadManager.getInstance(FaixaEtariaItem.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FaixaEtariaItem.PROP_ID, FaixaEtariaItemPK.PROP_FAIXA_ETARIA), faixaEtaria))
                    .addSorter(new QueryCustom.QueryCustomSorter(FaixaEtariaItem.PROP_PRIORIDADE, BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();

            for (FaixaEtariaItem faixaEtariaItem : faixaEtariaItensList) {
                ddFaixaEtariaItem.addChoice(faixaEtariaItem, faixaEtariaItem.getDescricao());
            }

        }

        if (target != null) {
            target.add(ddFaixaEtariaItem);
        }
    }

    private void criarDDFormaApresentacao(RelatorioRelacaoVacinasAplicadasDTOParam proxy) {
        ddFormaApresentacao = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.values());
        ddFormaApresentacao.add(new AjaxFormComponentUpdatingBehavior("change") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                updateCheckExibirGruposVazios(target);
            }
        });

        ddFormaApresentacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                ddTipoRelatorio.setEnabled(true);
                if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.CBO.equals(ddFormaApresentacao.getComponentValue())) {
                    ddTipoRelatorio.setComponentValue(RelatorioRelacaoVacinasAplicadasDTOParam.TipoRelatorio.DETALHADO);
                    ddTipoRelatorio.setEnabled(false);
                }
                if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.PACIENTE.equals(ddFormaApresentacao.getComponentValue())
                        && ddTipoRelatorio.getComponentValue().equals(RelatorioRelacaoVacinasAplicadasDTOParam.TipoRelatorio.RESUMIDO)) {
                    ddExibirDoses.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddExibirDoses.setEnabled(true);
                    ddDataNascimento.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddDataNascimento.setEnabled(true);
                    ddLoteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddLoteVacina.setEnabled(true);
                    ddDataValidadeVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddDataValidadeVacina.setEnabled(true);
                    ddFabricanteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddFabricanteVacina.setEnabled(true);
                } else if (ddTipoRelatorio.getComponentValue().equals(RelatorioRelacaoVacinasAplicadasDTOParam.TipoRelatorio.DETALHADO)) {
                    ddExibirDoses.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddExibirDoses.setEnabled(false);
                    ddDataNascimento.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddDataNascimento.setEnabled(false);
                    ddLoteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddLoteVacina.setEnabled(false);
                    ddDataValidadeVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddDataValidadeVacina.setEnabled(false);
                    ddFabricanteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddFabricanteVacina.setEnabled(false);
                } else {
                    ddTipoRelatorio.setEnabled(true);
                    ddExibirDoses.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddExibirDoses.setEnabled(false);
                    ddDataNascimento.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddDataNascimento.setEnabled(false);
                    ddLoteVacina.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddLoteVacina.setEnabled(false);
                    ddDataValidadeVacina.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddDataValidadeVacina.setEnabled(false);
                    ddFabricanteVacina.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddFabricanteVacina.setEnabled(false);
                }

                target.add(ddExibirDoses, ddDataNascimento, ddLoteVacina,
                        ddDataValidadeVacina, ddFabricanteVacina, ddFormaApresentacao, ddTipoRelatorio
                );
            }
        });
    }

    private void criarDDTipoRelatorio(RelatorioRelacaoVacinasAplicadasDTOParam proxy) {
        ddTipoRelatorio = DropDownUtil.getEnumDropDown(path(proxy.getTipoRelatorio()), RelatorioRelacaoVacinasAplicadasDTOParam.TipoRelatorio.values());
        ddTipoRelatorio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                ddFormaApresentacao.setEnabled(true);

                if (RelatorioRelacaoVacinasAplicadasDTOParam.TipoRelatorio.RESUMIDO.equals(ddTipoRelatorio.getComponentValue())) {
                    ddOrdenacao.setEnabled(false);
                    ddExibirDoses.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddExibirDoses.setEnabled(true);
                    ddDataNascimento.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddDataNascimento.setEnabled(true);
                    ddLoteVacina.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddLoteVacina.setEnabled(true);
                    ddDataValidadeVacina.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                    ddDataValidadeVacina.setEnabled(true);
                    ddFabricanteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddFabricanteVacina.setEnabled(false);

                    if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.PACIENTE.equals(ddFormaApresentacao.getComponentValue())) {
                        ddExibirDoses.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                        ddExibirDoses.setEnabled(true);
                        ddDataNascimento.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                        ddDataNascimento.setEnabled(true);
                        ddLoteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                        ddLoteVacina.setEnabled(true);
                        ddDataValidadeVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                        ddDataValidadeVacina.setEnabled(true);
                        ddFabricanteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                        ddFabricanteVacina.setEnabled(true);
                    } else {
                        ddExibirDoses.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                        ddExibirDoses.setEnabled(false);
                        ddDataNascimento.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                        ddDataNascimento.setEnabled(false);
                        ddLoteVacina.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                        ddLoteVacina.setEnabled(false);
                        ddDataValidadeVacina.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                        ddDataValidadeVacina.setEnabled(false);
                        ddFabricanteVacina.setComponentValue(RepositoryComponentDefault.NAO_LONG);
                        ddFabricanteVacina.setEnabled(false);
                    }
                } else {
                    ddOrdenacao.setEnabled(true);
                    ddExibirDoses.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddExibirDoses.setEnabled(false);
                    ddDataNascimento.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddDataNascimento.setEnabled(false);
                    ddLoteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddLoteVacina.setEnabled(false);
                    ddDataValidadeVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddDataValidadeVacina.setEnabled(false);
                    ddFabricanteVacina.setComponentValue(RepositoryComponentDefault.SIM_LONG);
                    ddFabricanteVacina.setEnabled(false);
                }

                target.add(ddExibirDoses, ddDataNascimento, ddLoteVacina,
                        ddDataValidadeVacina, ddFabricanteVacina, ddOrdenacao, ddFormaApresentacao
                );
            }
        });
    }

    private void createDDArea(RelatorioRelacaoVacinasAplicadasDTOParam proxy) {
        ddArea = new DropDown<>(path(proxy.getEquipeArea()));

        List<EquipeArea> list = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), br.com.celk.system.session.ApplicationSession.get().getSession().getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                .start().getList();

        ddArea.addChoice(null, BundleManager.getString("todas"));
        for (EquipeArea equipeArea : list) {
            ddArea.addChoice(equipeArea, equipeArea.getDescricao());
        }

        ddArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                EquipeArea equipeArea = ddArea.getComponentValue();
                List<EquipeMicroArea> equipeMicroAreas = Collections.emptyList();

                if (equipeArea != null) {
                    equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                            .addProperty(EquipeMicroArea.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                            .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                            .start().getList();
                }

                ddMicroArea.removeAllChoices();
                ddMicroArea.limpar(target);
                ddMicroArea.addChoice(null, BundleManager.getString("todas"));
                for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                    String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                    ddMicroArea.addChoice(equipeMicroArea, descricao);
                }

                target.add(ddMicroArea);
            }
        });
    }

    private void createDDEstrategia(RelatorioRelacaoVacinasAplicadasDTOParam proxy) {
        ddEstrategia = new DropDown(path(proxy.getCalendario()));
        List<Calendario> calendariosList = LoadManager.getInstance(Calendario.class)
                .addSorter(new QueryCustom.QueryCustomSorter(Calendario.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(calendariosList)) {
            for (Calendario calendario : calendariosList) {
                ddEstrategia.addChoice(null, Bundle.getStringApplication("rotulo_todos_maiusculo"));
                ddEstrategia.addChoice(calendario, calendario.getDescricao());
            }
        }
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioRelacaoVacinasAplicadasDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioRelacaoVacinasAplicadasDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(VacinaReportFacade.class).relatorioRelacaoVacinasAplicadas(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoVacinasAplicadas");
    }
}
