package br.com.celk.view.comunicacao.mensagem;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.TemplatePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.comunicacao.mensagem.panel.EntradaPanel;
import br.com.celk.view.comunicacao.mensagem.panel.EnviadosPanel;
import br.com.celk.view.comunicacao.mensagem.panel.EnviarMensagemPanel;
import br.com.celk.view.comunicacao.mensagem.panel.IMensagemPanel;
import br.com.celk.view.comunicacao.mensagem.panel.LixeiraPanel;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.CssReferenceHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.request.resource.CssResourceReference;


/**
 * <AUTHOR>
 */
@Private

public class CaixaMensagensPage extends TemplatePage {

    private static final String PANEL_ID = "viewPanel";
    private WebMarkupContainer panelContainer;
    private Panel activePanel;
    private IMensagemController mensagemController;

    public CaixaMensagensPage() {
        init();
        changePanel(new EntradaPanel(PANEL_ID));
    }

    public CaixaMensagensPage(QueryConsultaMensagensDTO dto) {
        init();
        EntradaPanel entradaPanel;
        changePanel(entradaPanel = new EntradaPanel(PANEL_ID));
        entradaPanel.setMensagem(dto);
    }

    private void init() {
        add(panelContainer = new WebMarkupContainer("panelContainer"));
        panelContainer.setOutputMarkupId(true);

        add(new AbstractAjaxLink("linkNovaMensagem") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getMensagemController().novaMensagem(target);
            }
        });

        add(new AbstractAjaxLink("linkEntrada") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getMensagemController().caixaEntrada(target);
            }
        });

        add(new AbstractAjaxLink("linkEnviados") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getMensagemController().caixaSaida(target);
            }
        });

        add(new AbstractAjaxLink("linkLixeira") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                getMensagemController().lixeira(target);
            }
        });
    }

    private <T extends IMensagemPanel> void changePanel(T panel) {
        if (activePanel != null) {
            activePanel.replaceWith((Panel) panel);
        } else {
            panelContainer.add((Panel) panel);
        }
        panel.setMensagemController(getMensagemController());
        activePanel = (Panel) panel;
    }

    private <T extends IMensagemPanel> void changePanel(AjaxRequestTarget target, T panel) {
        changePanel(panel);
        target.add(panelContainer);
    }

    public IMensagemController getMensagemController() {
        if (this.mensagemController == null) {
            this.mensagemController = new IMensagemController() {

                @Override
                public void novaMensagem(AjaxRequestTarget target) {
                    changePanel(target, new EnviarMensagemPanel(PANEL_ID));
                }

                @Override
                public void encaminharMensagem(AjaxRequestTarget target, Mensagem mensagem) {
                    EnviarMensagemPanel enviarMensagemPanel = new EnviarMensagemPanel(PANEL_ID);
                    enviarMensagemPanel.encaminharMensagem(mensagem);
                    changePanel(target, enviarMensagemPanel);
                }

                @Override
                public void responderMensagem(AjaxRequestTarget target, Mensagem mensagem) {
                    EnviarMensagemPanel enviarMensagemPanel = new EnviarMensagemPanel(PANEL_ID);
                    enviarMensagemPanel.responderMensagem(mensagem);
                    changePanel(target, enviarMensagemPanel);
                }

                @Override
                public void caixaEntrada(AjaxRequestTarget target) {
                    changePanel(target, new EntradaPanel(PANEL_ID));
                }


                @Override
                public void caixaSaida(AjaxRequestTarget target) {
                    changePanel(target, new EnviadosPanel(PANEL_ID));
                }

                @Override
                public void lixeira(AjaxRequestTarget target) {
                    changePanel(target, new LixeiraPanel(PANEL_ID));
                }

                @Override
                public QueryConsultaMensagensDTO marcarComoLida(AjaxRequestTarget target, QueryConsultaMensagensDTO dto) {
                    try {
                        if (RepositoryComponentDefault.SimNaoLong.NAO.value().equals(dto.getMensagem().getLida())) {
                            dto.getMensagem().setLida(RepositoryComponentDefault.SimNaoLong.SIM.value());
                            dto.getMensagem().setDataLeitura(DataUtil.getDataAtual());
                            dto.setMensagem(BOFactoryWicket.save(dto.getMensagem()));
                            if (ApplicationSession.get().getMensagensNovasCache().contains(dto.getMensagem())) {
                                ApplicationSession.get().getMensagensNovasCache().remove(dto.getMensagem());
                            }
                            ApplicationSession.get().subtrairQuantidadeMensagensUsuario();
                            if (target != null) {
                                notificarMensagem(target, dto);
                            }
                        }
                    } catch (DAOException | ValidacaoException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                    return dto;
                }

                @Override
                public void notificarMensagem(AjaxRequestTarget target, QueryConsultaMensagensDTO dtoMessage) {
                    CaixaMensagensPage.super.notifyMessage(target, dtoMessage);
                }
            };
        }

        return this.mensagemController;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("caixaMensagens");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssReferenceHeaderItem.forReference(new CssResourceReference(CaixaMensagensPage.class, "CaixaMensagensPage.css")));
    }

    @Override
    public void notifyMessage(AjaxRequestTarget target, QueryConsultaMensagensDTO dto) {
        super.notifyMessage(target, dto); //To change body of generated methods, choose Tools | Templates.
        ((IMensagemPanel) activePanel).atualizarMensagens(target);
    }
}
