package br.com.celk.view.cadsus.usuariocadsus.esus.tabbedpanel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.view.cadsus.enderecousuariocadsus.DlgCadastroEnderecoUsuarioCadsus;
import br.com.celk.view.cadsus.usuariocadsus.CadastroUsuarioCidadaoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.EnderecoUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.EnderecoUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.IdentificacaoFamiliaDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.EnderecoUsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class EnderecoUsuarioCidadaoTab extends TabPanel<CadastroUsuarioCidadaoDTO> {

    private final UsuarioCadsus usuarioCadsus;
    private SelectionPageableTable<EnderecoUsuarioCadsusDTO> tblEnderecos;
    private Table<UsuarioCadsus> tblMoradores;
    private DlgCadastroEnderecoUsuarioCadsus dlgCadastroEnderecoUsuarioCadsus;
    private EnderecoUsuarioCadsusDTOParam param;
    private InputField txtEndereco;
    private WebMarkupContainer containerIdentificacaoFamilia;
    private List<UsuarioCadsusEndereco> enderecoList;
    private ProcurarButton<EnderecoUsuarioCadsusDTOParam> btnProcurar;
    private AbstractAjaxButton btnNovo;
    private AbstractAjaxButton btnCopiar;
    private boolean utilizaEnderecoEstruturado;

    private Table tblHistoricoEnderecos;


    public EnderecoUsuarioCidadaoTab(String id, CadastroUsuarioCidadaoDTO object) {
        super(id, object);
        usuarioCadsus = object.getUsuarioCadsus();
        init();
    }

    public void init() {
        try {
            utilizaEnderecoEstruturado = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("utilizaEnderecoEstruturado"));
        } catch (ValidacaoRuntimeException | DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        WebMarkupContainer containerEndereco = new WebMarkupContainer("containerEndereco", new CompoundPropertyModel<EnderecoUsuarioCadsusDTOParam>(param = new EnderecoUsuarioCadsusDTOParam()));
        containerIdentificacaoFamilia = new WebMarkupContainer("containerIdentificacaoFamilia", new CompoundPropertyModel<IdentificacaoFamiliaDTO>(new IdentificacaoFamiliaDTO()));

        txtEndereco = new InputField("endereco");

        containerEndereco.add(txtEndereco);

        containerEndereco.add(tblEnderecos = new SelectionPageableTable<EnderecoUsuarioCadsusDTO>("tblEnderecos", getColumnsEndereco(), getPagerProviderEndereco(), 10) {

            @Override
            public void update(AjaxRequestTarget target) {
                super.update(target);
                EnderecoUsuarioCidadaoTab.this.object.setEnderecoUsuarioCadsus(null);
                tblMoradores.limpar(target);
            }

        });

        containerEndereco.add(tblMoradores = new Table<UsuarioCadsus>("tblMoradores", getColumnsMoradores(), getCollectionProviderMoradores()));

        containerEndereco.add(btnProcurar = new ProcurarButton<EnderecoUsuarioCadsusDTOParam>("btnProcurar", tblEnderecos) {

            @Override
            public EnderecoUsuarioCadsusDTOParam getParam() {
                return param;
            }

            @Override
            public void antesProcurar(AjaxRequestTarget target) {
                IModel iModel = containerIdentificacaoFamilia.getDefaultModel();
                iModel.setObject(new IdentificacaoFamiliaDTO());
                target.add(containerIdentificacaoFamilia);
            }

        });

        containerEndereco.add(btnNovo = new AbstractAjaxButton("btnNovo") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                novoEndereco(target);
            }
        });

        containerEndereco.add(btnCopiar = new AbstractAjaxButton("btnCopiar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                copiarEndereco(target);
            }
        });

        if (!liberaBotoesAtendimentoOutraUnidade()) {
            if (usuarioCadsus.getEnderecoDomicilio() != null) {
                getFeedbackMessages().warn(this, bundle("msgPacienteVinculadoFamiliaNaoAlterarEndereco"));
                btnProcurar.setEnabled(false);
                btnNovo.setEnabled(false);
                btnCopiar.setEnabled(false);
            } else if (utilizaEnderecoEstruturado) {
                btnNovo.setVisible(false);
                btnCopiar.setVisible(false);
            }

        }


        tblEnderecos.addSelectionAction(new ISelectionAction<EnderecoUsuarioCadsusDTO>() {

            @Override
            public void onSelection(AjaxRequestTarget target, EnderecoUsuarioCadsusDTO object) {
                EnderecoUsuarioCidadaoTab.this.object.setEnderecoUsuarioCadsus(object.getEnderecoUsuarioCadsus());
                tblMoradores.populate(target);
                target.add(containerIdentificacaoFamilia);
            }
        });

        if (object.getEnderecoUsuarioCadsus() != null && object.getEnderecoUsuarioCadsus().getCodigo() != null) {
            setDadosEnderecoPaciente(object.getEnderecoUsuarioCadsus());
            tblEnderecos.populate();
            tblMoradores.populate();
        }

        IdentificacaoFamiliaDTO proxy = on(IdentificacaoFamiliaDTO.class);

        containerIdentificacaoFamilia.add(new DisabledInputField(path(proxy.getCodigoDomicilio())));
        containerIdentificacaoFamilia.add(new DisabledInputField(path(proxy.getArea())));
        containerIdentificacaoFamilia.add(new DisabledInputField(path(proxy.getMicroArea())));
        containerIdentificacaoFamilia.add(new DisabledInputField(path(proxy.getNumeroFamilia())));
        containerIdentificacaoFamilia.add(new DisabledInputField(path(proxy.getSegmento())));
        containerIdentificacaoFamilia.add(new DisabledInputField(path(proxy.getAgenteComunitario())));
        containerIdentificacaoFamilia.add(new DisabledInputField(path(proxy.getUnidade())));
        containerIdentificacaoFamilia.setOutputMarkupId(true);

        containerEndereco.add(containerIdentificacaoFamilia);
        containerEndereco.add(tblHistoricoEnderecos = new Table("tblHistoricoEnderecos", getColumnsHistoricoEndereco(), getProviderHistoricoEndereco()));
        tblHistoricoEnderecos.populate();

        add(containerEndereco);

        carregarEnderecosPaciente();
    }

    public boolean liberaBotoesAtendimentoOutraUnidade() {
        try {
            boolean permiteAlterarEndereçoDomicilioOutraUnidade = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("permiteAlterarEndereçoDomicilioOutraUnidade"));

            if (object.getEnderecoUsuarioCadsus() == null) {
                return false;
            }
            List<EnderecoDomicilio> domiciliosList = LoadManager.getInstance(EnderecoDomicilio.class)
                    .addProperties(new HQLProperties(EnderecoDomicilio.class).getProperties())
                    .addProperties(new HQLProperties(EquipeMicroArea.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA)).getProperties())
                    .addProperties(new HQLProperties(EquipeArea.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA)).getProperties())
                    .addProperties(new HQLProperties(SegmentoTerritorial.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CODIGO), object.getEnderecoUsuarioCadsus().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(EnderecoDomicilio.PROP_EXCLUIDO, BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.NAO_EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                    .start().getList();

            if (usuarioCadsus.getEnderecoDomicilio() != null && permiteAlterarEndereçoDomicilioOutraUnidade
                    && isAtendimentoOutraUnidade()
                    && familiaSemAgenteComunitario(domiciliosList.get(0))) {
                getFeedbackMessages().warn(this, bundle("msgPacienteVinculadoFamiliaeSemAgente"));
                btnProcurar.setEnabled(false);
                btnNovo.setEnabled(true);
                btnCopiar.setEnabled(true);
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    public boolean isAtendimentoOutraUnidade() {
        Empresa empresaLogado = ApplicationSession.get().getSession().getEmpresa();
        return !empresaLogado.equals(object.getEnderecoUsuarioCadsus().getEmpresa());
    }

    private void carregarEnderecosPaciente() {
        UsuarioCadsusEndereco proxy = on(UsuarioCadsusEndereco.class);
        enderecoList = new ArrayList<UsuarioCadsusEndereco>();

        if (object.getUsuarioCadsus().getCodigo() != null) {
            enderecoList = LoadManager.getInstance(UsuarioCadsusEndereco.class)
                    .addProperties(new HQLProperties(UsuarioCadsusEndereco.class).getProperties())
                    .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, path(proxy.getId().getEndereco())).getProperties())
                    .addProperty(path(proxy.getId().getEndereco().getCidade().getEstado().getCodigo()))
                    .addProperty(path(proxy.getId().getEndereco().getCidade().getEstado().getDescricao()))
                    .addProperty(path(proxy.getId().getEndereco().getCidade().getEstado().getSigla()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getUsuarioCadsus().getCodigo()), object.getUsuarioCadsus().getCodigo()))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataUsuario()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .start().getList();
        }
    }

    private ICollectionProvider getProviderHistoricoEndereco() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return enderecoList;
            }
        };
    }

    private List<IColumn> getColumnsHistoricoEndereco() {
        List<IColumn> columns = new ArrayList<IColumn>();
        UsuarioCadsusEndereco proxy = on(UsuarioCadsusEndereco.class);

        columns.add(createColumn(bundle("endereco"), proxy.getId().getEndereco().getEnderecoFormatadoComCidade()));
        columns.add(createColumn(bundle("dataInclusao"), proxy.getDataCadastro()));
        columns.add(createColumn(bundle("dataAlteracao"), proxy.getDataUsuario()));
        columns.add(createColumn(bundle("situacao"), proxy.getDescricaoStatus()));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));
        columns.add(createColumn(bundle("unidade"), proxy.getId().getEndereco().getEmpresa().getDescricao()));

        return columns;
    }

    public List<ISortableColumn<EnderecoUsuarioCadsusDTO>> getColumnsEndereco() {
        List<ISortableColumn<EnderecoUsuarioCadsusDTO>> columns = new ArrayList<ISortableColumn<EnderecoUsuarioCadsusDTO>>();
        ColumnFactory columnFactory = new ColumnFactory(EnderecoUsuarioCadsusDTO.class);

        columns.add(columnFactory.createColumn(BundleManager.getString("rua"), VOUtils.montarPath(EnderecoUsuarioCadsusDTO.PROP_RUA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("complemento"), VOUtils.montarPath(EnderecoUsuarioCadsusDTO.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_COMPLEMENTO_LOGRADOURO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("bairro"), VOUtils.montarPath(EnderecoUsuarioCadsusDTO.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_BAIRRO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("numero"), VOUtils.montarPath(EnderecoUsuarioCadsusDTO.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_NUMERO_LOGRADOURO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("cep"), VOUtils.montarPath(EnderecoUsuarioCadsusDTO.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CEP)));
        columns.add(columnFactory.createColumn(BundleManager.getString("cidade"), VOUtils.montarPath(EnderecoUsuarioCadsusDTO.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CIDADE, Cidade.PROP_DESCRICAO)));

        return columns;
    }

    public IPagerProvider getPagerProviderEndereco() {
        return new QueryPagerProvider() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(EnderecoUsuarioCadsusFacade.class).getPagerConsultaEndereco(dataPaging);
            }
        };
    }

    public List<ISortableColumn<UsuarioCadsus>> getColumnsMoradores() {
        List<ISortableColumn<UsuarioCadsus>> columns = new ArrayList<ISortableColumn<UsuarioCadsus>>();
        ColumnFactory columnFactory = new ColumnFactory(UsuarioCadsus.class);

        columns.add(columnFactory.createColumn(BundleManager.getString("paciente"), VOUtils.montarPath(UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createColumn(BundleManager.getString("sexo"), VOUtils.montarPath(UsuarioCadsus.PROP_SEXO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("idade"), VOUtils.montarPath(UsuarioCadsus.PROP_DESCRICAO_IDADE_SIMPLES)));

        return columns;
    }

    public ICollectionProvider getCollectionProviderMoradores() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getUsuariosEndereco(tblEnderecos.getSelectedObject().getEnderecoUsuarioCadsus());
            }
        };
    }

    public List<UsuarioCadsus> getUsuariosEndereco(EnderecoUsuarioCadsus enderecoUsuarioCadsus) throws DAOException, ValidacaoException {
        List<UsuarioCadsus> moradores = new ArrayList<UsuarioCadsus>();

        List<EnderecoDomicilio> domiciliosList = LoadManager.getInstance(EnderecoDomicilio.class)
                .addProperties(new HQLProperties(EnderecoDomicilio.class).getProperties())
                .addProperties(new HQLProperties(EquipeMicroArea.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA)).getProperties())
                .addProperties(new HQLProperties(EquipeArea.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA)).getProperties())
                .addProperties(new HQLProperties(SegmentoTerritorial.class, VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_SEGMENTO_TERRITORIAL)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CODIGO), enderecoUsuarioCadsus.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(EnderecoDomicilio.PROP_EXCLUIDO, BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.NAO_EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                .setMaxResults(1)
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(domiciliosList)) {
            produrarInformacoesFamilia(domiciliosList.get(0));

            List<UsuarioCadsusDomicilio> lista = LoadManager.getInstance(UsuarioCadsusDomicilio.class)
                    .addProperties(new HQLProperties(UsuarioCadsusDomicilio.class).getProperties())
                    .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(UsuarioCadsusDomicilio.PROP_USUARIO_CADSUS)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDomicilio.PROP_ENDERECO_DOMICILIO), domiciliosList.get(0)))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDomicilio.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, UsuarioCadsusDomicilio.STATUS_EXCLUIDO))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(lista)) {
                for (UsuarioCadsusDomicilio _usuarioCadsusDomicilio : lista) {
                    moradores.add(_usuarioCadsusDomicilio.getUsuarioCadsus());
                }
            }
        } else {
            IModel iModel = containerIdentificacaoFamilia.getDefaultModel();
            iModel.setObject(new IdentificacaoFamiliaDTO());
            carregarEnderecosPaciente();

            List<UsuarioCadsusEndereco> lista = LoadManager.getInstance(UsuarioCadsusEndereco.class)
                    .addProperties(new HQLProperties(UsuarioCadsusEndereco.class).getProperties())
                    .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_USUARIO_CADSUS)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusEndereco.PROP_STATUS), BuilderQueryCustom.QueryParameter.DIFERENTE, UsuarioCadsusEndereco.STATUS_CANCELADO))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_ENDERECO), enderecoUsuarioCadsus))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(lista)) {
                for (UsuarioCadsusEndereco _usuarioCadsusEndereco : lista) {
                    moradores.add(_usuarioCadsusEndereco.getId().getUsuarioCadsus());
                }
            }
        }
        return moradores;

    }

    public boolean familiaSemAgenteComunitario(EnderecoDomicilio ed) throws DAOException, ValidacaoException {

        IdentificacaoFamiliaDTO identificacaoFamiliaDTO = new IdentificacaoFamiliaDTO();
        identificacaoFamiliaDTO.setMicroArea(ed.getEquipeMicroArea().getMicroArea());

        if (ed.getEquipeMicroArea() != null && ed.getCidade() != null)
            identificacaoFamiliaDTO = procurarAgenteComunitario(identificacaoFamiliaDTO, ed.getEquipeMicroArea().getEquipeArea(), ed.getCidade());

        return ed.getNumeroFamilia() != null && identificacaoFamiliaDTO.getAgenteComunitario() == null;
    }

    private void produrarInformacoesFamilia(EnderecoDomicilio ed) throws DAOException, ValidacaoException {
        IdentificacaoFamiliaDTO identificacaoFamiliaDTO = new IdentificacaoFamiliaDTO();

        identificacaoFamiliaDTO.setCodigoDomicilio(ed.getCodigo());
        if (ed.getEquipeMicroArea() != null) {
            identificacaoFamiliaDTO.setMicroArea(ed.getEquipeMicroArea().getMicroArea());
            if (ed.getEquipeMicroArea().getEquipeArea().getSegmentoTerritorial() != null && ed.getEquipeMicroArea().getEquipeArea().getSegmentoTerritorial().getDescricao() != null) {
                identificacaoFamiliaDTO.setSegmento(ed.getEquipeMicroArea().getEquipeArea().getSegmentoTerritorial().getDescricao());
            }
        }
        if (ed.getNumeroFamilia() != null) {
            identificacaoFamiliaDTO.setNumeroFamilia(ed.getNumeroFamilia());
        }
        if (ed.getEquipeMicroArea() != null && ed.getCidade() != null) {
            procurarAgenteComunitario(identificacaoFamiliaDTO, ed.getEquipeMicroArea().getEquipeArea(), ed.getCidade());
        }

        if (ed.getEquipeMicroArea() != null) {
            EquipeArea area = LoadManager.getInstance(EquipeArea.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CODIGO), ed.getEquipeMicroArea().getEquipeArea().getCodigo()))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                    .start().getVO();

            identificacaoFamiliaDTO.setArea(area.getDescricao());
        }

        IModel iModel = containerIdentificacaoFamilia.getDefaultModel();
        iModel.setObject(identificacaoFamiliaDTO);

    }

    public IdentificacaoFamiliaDTO procurarAgenteComunitario(IdentificacaoFamiliaDTO dto, EquipeArea equipeArea, Cidade cidade) throws DAOException, ValidacaoException {
        if (equipeArea != null && dto.getMicroArea() != null) {

            List<EquipeProfissional> epList = LoadManager.getInstance(EquipeProfissional.class)
                    .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL)).getProperties())
                    .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA), equipeArea))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_MICRO_AREA), dto.getMicroArea()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EQUIPE_AREA, EquipeArea.PROP_CIDADE, Cidade.PROP_CODIGO), cidade.getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_ATIVO), RepositoryComponentDefault.SIM))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_STATUS), EquipeProfissional.STATUS_ATIVO))
                    .start().getList();

            if (!epList.isEmpty()) {
                EquipeProfissional ep = epList.get(0);
                dto.setAgenteComunitario(ep.getProfissional().getNome());
                dto.setUnidade(ep.getEquipe().getEmpresa().getDescricao());
            }
        }

        return dto;
    }

    private void novoEndereco(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        getDlgCadastroEnderecoUsuarioCadsus(target).limpar(target);
        getDlgCadastroEnderecoUsuarioCadsus(target).show(target);
    }

    private void copiarEndereco(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        EnderecoUsuarioCadsusDTO selectedObject = tblEnderecos.getSelectedObject();
        if (selectedObject != null) {
            getDlgCadastroEnderecoUsuarioCadsus(target).limpar(target);
            getDlgCadastroEnderecoUsuarioCadsus(target).setObject(new DefinerPropertiesCloning().define(selectedObject.getEnderecoUsuarioCadsus()));
            getDlgCadastroEnderecoUsuarioCadsus(target).show(target);
        } else {
            throw new ValidacaoException(BundleManager.getString("selecioneUmEndereco"));
        }
    }

    private DlgCadastroEnderecoUsuarioCadsus getDlgCadastroEnderecoUsuarioCadsus(AjaxRequestTarget target) {
        if (this.dlgCadastroEnderecoUsuarioCadsus == null) {
            WindowUtil.addModal(target, this, dlgCadastroEnderecoUsuarioCadsus = new DlgCadastroEnderecoUsuarioCadsus(WindowUtil.newModalId(this)));
            dlgCadastroEnderecoUsuarioCadsus.add(new ICadastroListener<EnderecoUsuarioCadsus>() {

                @Override
                public void onSalvar(AjaxRequestTarget target, EnderecoUsuarioCadsus object) throws DAOException, ValidacaoException {
                    validaCepEnderecoPaciente(object);

                    setDadosEnderecoPaciente(object);
                    tblEnderecos.populate(target);
                    EnderecoUsuarioCidadaoTab.this.object.setEnderecoUsuarioCadsus(object);
                }
            });
        }
        return dlgCadastroEnderecoUsuarioCadsus;
    }

    private void validaCepEnderecoPaciente(EnderecoUsuarioCadsus euc) throws ValidacaoException {
        if (!utilizaEnderecoEstruturado && (euc.getCep() == null || euc.getCep().isEmpty())) {
            throw new ValidacaoException(BundleManager.getString("cepObrigatorio"));
        }
    }

    @Override
    public void onAjaxUpdate(AjaxRequestTarget target) {
        super.onAjaxUpdate(target);
        if (Objects.isNull(object.getUsuarioCadsus().getCodigo())) {
            try {
                EnderecoUsuarioCadsus euc = BOFactory.getBO(EnderecoUsuarioCadsusFacade.class).save(object.getUsuarioCadsus().getEnderecoUsuarioCadsus());
                setDadosEnderecoPaciente(euc);
                tblEnderecos.populate(target);
            } catch (DAOException | ValidacaoException e) {
                Loggable.log.error(e.getMessage(), e);
            }
        }
    }

    private void setDadosEnderecoPaciente(EnderecoUsuarioCadsus euc) {
        EnderecoUsuarioCadsusDTOParam eucDtoParam = new EnderecoUsuarioCadsusDTOParam();
        EnderecoUsuarioCadsusDTO enderecoUsuarioCadsusDTO = new EnderecoUsuarioCadsusDTO();

        eucDtoParam.setCodigo(euc.getCodigo());
        tblEnderecos.getDataProvider().setParameters(eucDtoParam);
        enderecoUsuarioCadsusDTO.setEnderecoUsuarioCadsus(euc);
        tblEnderecos.setSelectedObject(enderecoUsuarioCadsusDTO);
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("endereco");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return this.txtEndereco;
    }

}
