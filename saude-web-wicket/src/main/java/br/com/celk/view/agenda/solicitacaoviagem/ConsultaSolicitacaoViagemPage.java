package br.com.celk.view.agenda.solicitacaoviagem;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.TimeColumn;
import br.com.celk.component.treetable.pageable.PageableTreeTable;
import br.com.celk.component.treetable.pageable.customdatatable.ICustomColumnTreeTable;
import br.com.celk.component.treetable.pageable.customdatatable.ITreeColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.solicitacaoviagem.columnpanel.ConsultaSolicitacaoViagemColumnPanel;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem;
import com.google.common.base.Strings;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaSolicitacaoViagemPage extends BasePage {

    private String paciente;
    private String local;
    private Long status;
    private DropDown dropDownStatus;
    private Date dataViagem = DataUtil.getDataAtual();

    private PageableTreeTable tblViagens;
    private SolicitacaoViagemTreeProvider provider = new SolicitacaoViagemTreeProvider();
    
    public ConsultaSolicitacaoViagemPage() {
        initForm();
    }

    @Override
    protected void postConstruct() {
        super.postConstruct();
        procurar(null);
    }



    public void initForm() {
        Form form = new Form("form");

        form.add(new InputField("paciente", new PropertyModel(this, "paciente")));
        form.add(new InputField("local", new PropertyModel(this, "local")));
        form.add(new DateChooser("dataViagem", new PropertyModel(this, "dataViagem")));

        form.add(dropDownStatus = getDropDownStatus("status", new PropertyModel(this, "status")));
        dropDownStatus.setComponentValue(SolicitacaoViagem.Status.PENDENTE.value());

        form.add(new AbstractAjaxButton("btnProcurar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarProcurar();
                procurar(target);
            }
        });

        form.add(tblViagens = new PageableTreeTable("tblViagens", getColumns(), provider, 10));


        form.add(new AbstractAjaxButton("btnNovo") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(CadastroSolicitacaoViagemPage.class);
            }
        });

        add(form);

    }

    private void procurar(AjaxRequestTarget target){
        SolicitacaoViagemDTOParam param = new SolicitacaoViagemDTOParam();
        param.setLocal(local);
        param.setPaciente(paciente);
        param.setSituacao(status);
        param.setDataViagem(dataViagem);
        provider.recarregar(param);

        if (target!=null) {
            target.add(tblViagens);
        }
    }

    private void validarProcurar() throws ValidacaoException {
        if (Strings.isNullOrEmpty(paciente) && dataViagem == null) {
            throw new ValidacaoException(BundleManager.getString("msgAplicarFiltroPacienteOuData"));
        }
    }


    private DropDown getDropDownStatus(String id, PropertyModel model) {
        DropDown dropDown = new DropDown(id, model);
        dropDown.addChoice("", BundleManager.getString("todos"));
        for (SolicitacaoViagem.Status status : SolicitacaoViagem.Status.values()) {
            dropDown.addChoice(status.value(), status.descricao());
        }
        return dropDown;
    }

    public List getColumns() {
        List<IColumn<SolicitacaoViagemDTO, String>> result = new LinkedList();
        int headerIndex = 0;

        SolicitacaoViagemDTO proxy = on(SolicitacaoViagemDTO.class);

        result.add(new ITreeColumn(Model.of("")));
        result.add(getCustomColumn());
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getUsuarioCadsus().getNomeSocial()), path(proxy.getSolicitacaoViagemLeaf().getAcompanhante().getNomeSocial()), headerIndex++, Model.of(BundleManager.getString("paciente")), true));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getUsuarioCadsus().getIdade()), null, headerIndex++, Model.of(BundleManager.getString("idade"))));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getDescricaoPcdSimNao()), null, headerIndex++, Model.of(BundleManager.getString("pcd"))));
        result.add(new DateColumn(bundle("dataViagem"), path(proxy.getSolicitacaoViagem().getDataViagem())));
        result.add(new TimeColumn(bundle("horario"), path(proxy.getSolicitacaoViagem().getHorario())));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getSolicitacaoViagemPrincipal().getDescricaoStatus()), null, headerIndex++, Model.of(BundleManager.getString("situacao"))));
        result.add(new ICustomColumnTreeTable(path(proxy.getSolicitacaoViagem().getLocal()), null, headerIndex++, Model.of(BundleManager.getString("local"))));
        return result;
    }

    private IColumn getCustomColumn() {
        return new CustomColumn<SolicitacaoViagemDTO>() {
            @Override
            public Component getComponent(String componentId, final SolicitacaoViagemDTO solicitacaoViagemDTO) {
                return new ConsultaSolicitacaoViagemColumnPanel(componentId, solicitacaoViagemDTO) {
                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        procurar(target);
                    }
                };
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaSolicitacoesViagem");
    }
}
