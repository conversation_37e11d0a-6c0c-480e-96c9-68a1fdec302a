package br.com.celk.view.home.mensagem;

import br.com.celk.component.messaging.MensagensNovaCache;
import br.com.celk.component.repeatingview.ReverseRepeatingView;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.comunicacao.mensagem.CaixaMensagensPage;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PanelMensagens extends Panel {

    private ReverseRepeatingView repeaterMensagens;
    private Label tituloMensagens;
    private Map<Long, PanelMensagem> messagePanels = new HashMap<Long, PanelMensagem>();

    public PanelMensagens(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        add(tituloMensagens = new Label("tituloMensagens", new LoadableDetachableModel() {

            @Override
            protected Object load() {
                return BundleManager.getString("vocePossuiXMensagensNaoLidas", MensagensNovaCache.countMessage());
            }
        }));
        tituloMensagens.setOutputMarkupId(true);

        add(repeaterMensagens = new ReverseRepeatingView("repeaterMensagens"));

        for (QueryConsultaMensagensDTO dto : MensagensNovaCache.get()) {
            addMensagem(dto);
        }

        add(new Link("linkTodasMensagens") {

            @Override
            public void onClick() {
                setResponsePage(CaixaMensagensPage.class);
            }
        });

    }

    public int getMessageCount() {
        return ApplicationSession.get().getQuantidadeMensagensUsuario().intValue();
    }

    public void addMensagem(QueryConsultaMensagensDTO dto) {
        PanelMensagem panelMensagem;
        repeaterMensagens.add(panelMensagem = new PanelMensagem(repeaterMensagens.newChildId(), dto));
        messagePanels.put(dto.getMensagem().getCodigo(), panelMensagem);
    }

    public void notifyMessage(AjaxRequestTarget target, QueryConsultaMensagensDTO dto) {
        PanelMensagem panelMensagem = messagePanels.get(dto.getMensagem().getCodigo());
        if (RepositoryComponentDefault.SimNaoLong.NAO.value().equals(dto.getMensagem().getLida()) && RepositoryComponentDefault.SimNaoLong.NAO.value().equals(dto.getMensagem().getExcluida())) {
            if (!ApplicationSession.get().getMensagensNovasCache().contains(dto.getMensagem())) {
                ApplicationSession.get().getMensagensNovasCache().add(dto.getMensagem());
            }
            ApplicationSession.get().adicinarQuantidadeMensagensUsuario();
            if (panelMensagem == null) {
                panelMensagem = new PanelMensagem(repeaterMensagens.newChildId(), dto);
                messagePanels.put(dto.getMensagem().getCodigo(), panelMensagem);
                repeaterMensagens.add((Component) panelMensagem);
                target.add(this);
            }
        } else if (panelMensagem != null) {
            messagePanels.remove(dto.getMensagem().getCodigo());
            repeaterMensagens.remove(panelMensagem);
            target.add(this);

            if (ApplicationSession.get().getMensagensNovasCache().contains(dto.getMensagem())) {
                ApplicationSession.get().getMensagensNovasCache().remove(dto.getMensagem());
            }
            ApplicationSession.get().subtrairQuantidadeMensagensUsuario();
        }
        target.add(tituloMensagens);
    }

}
