package br.com.celk.view.materiais.pedidotransferencia;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.panel.DetalhesActionColumnPanel;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.pedidotransferenciaitem.DlgDetalhesItemPedido;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.DTOPedidoTransferencia;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.DTOPedidoTransferenciaItem;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.OrigemProcessoPedido;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.UsuarioGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 *
 * <AUTHOR>
 */

public class DetalhesPedidoAlmoxarifadoPage extends BasePage {

    private PedidoTransferencia pedidoTransferencia;

    private static final String CONTROLADOR_BRANET = "Controlador Pedido Limite";
    private List<ISortableColumn<PedidoTransferenciaItem>> columns;
    private CollectionProvider collectionProvider;

    private Table table;
    private DlgDetalhesItemPedido dialogDetalhesItem;

    public DetalhesPedidoAlmoxarifadoPage(PedidoTransferencia pedidoTransferencia) {
        this.pedidoTransferencia = pedidoTransferencia;
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(pedidoTransferencia));

        form.add(new DisabledInputField("empresa.descricaoFormatado"));
        form.add(new DisabledInputField("dataPedido"));
        form.add(new DisabledInputField("codigo"));
        form.add(new DisabledInputField("empresaOrigem.descricaoFormatado"));
        form.add(new DisabledInputField("descricaoStatus"));
        form.add(new DisabledInputField("descricaoTipo"));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferencia.PROP_DEPOSITO, Deposito.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField("empresaDestino.descricaoFormatado"));
        form.add(new DisabledInputField("veiculo.descricaoFormatado"));
        form.add(new DisabledInputField("dataEmbarque"));
        form.add(new DisabledInputField("responsavelEntrega"));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferencia.PROP_USUARIO_SEPARACAO, Usuario.PROP_DESCRICAO_FORMATADO)));
        form.add(new DisabledInputField(VOUtils.montarPath(PedidoTransferencia.PROP_DATA_SEPARACAO)));
        form.add(new DisabledInputField("usuarioRecebimento.descricaoFormatado"));
        form.add(new DisabledInputField("dataRecebimento"));
        form.add(new DisabledInputField("usuario.descricaoFormatado"));
        form.add(new DisabledInputField("dataCadastro"));
        form.add(new DisabledInputField("usuarioCancelamento.descricaoFormatado"));
        form.add(new DisabledInputField("dataCancelamento"));
        form.add(new DisabledInputArea(VOUtils.montarPath(PedidoTransferencia.PROP_MOTIVO_CANCELAMENTO)));

        if (this.pedidoTransferencia.getStatus().equals(PedidoTransferencia.STATUS_PENDENTE_APROVACAO)) {
            form.add(table = new Table("table", getEditColumns(), getCollectionProvider()));
        } else {
            form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        }

        form.add(dialogDetalhesItem = new DlgDetalhesItemPedido("dialogDetalhesItem"));

        form.add(new VoltarButton("btnVoltar"));

        add(form);

        table.populate();
    }

    private List<ISortableColumn<PedidoTransferenciaItem>> getColumns() {
        if (this.columns == null) {
            this.columns = new ArrayList<ISortableColumn<PedidoTransferenciaItem>>();

            ColumnFactory columnFactory = new ColumnFactory(PedidoTransferenciaItem.class);
            PedidoTransferenciaItem proxy = on(PedidoTransferenciaItem.class);

            this.columns.add(getCustomColumn());
            try {
                if (RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("habilitarIntegracaoBranet"))) {
                    this.columns.add(columnFactory.createColumn(bundle("idSolicitacaoBranet"), path(proxy.getIdSolicitacaoItem())));
                }
            } catch (DAOException e) {
                br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
            this.columns.add(columnFactory.createColumn(bundle("item"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_ITEM)));
            this.columns.add(columnFactory.createColumn(bundle("produto"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
            this.columns.add(columnFactory.createColumn(bundle("un"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
            this.columns.add(columnFactory.createColumn(bundle("qtSolicitada"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE_SOLICITADA)));
            this.columns.add(columnFactory.createColumn(bundle("qtEnviada"), path(proxy.getQuantidadeEnviada())));
            this.columns.add(columnFactory.createColumn(bundle("naoAprovadaAbv"), path(proxy.getQuantidadeNaoAprovada())));
            this.columns.add(columnFactory.createColumn(bundle("semEstoque"), path(proxy.getQuantidadeSemEstoque())));
            this.columns.add(columnFactory.createColumn(bundle("qtRecebida"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE_RECEBIDA)));
            this.columns.add(columnFactory.createColumn(bundle("situacao"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_DESCRICAO_STATUS)));
            this.columns.add(columnFactory.createColumn(bundle("qtdUltimoPedido"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE_ULTIMO_PEDIDO)));
            this.columns.add(columnFactory.createColumn(bundle("consumoTrintaDiasAbv"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_CONSUMO_TRINTA_DIAS)));
            this.columns.add(columnFactory.createColumn(bundle("consumoNoventaDiasAbv"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_CONSUMO_NOVENTA_DIAS)));
            this.columns.add(columnFactory.createColumn(bundle("saldoAtual"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_SALDO_EMPRESA)));
        }

        return this.columns;
    }

    private CustomColumn<PedidoTransferenciaItem> getCustomColumn() {
        return new CustomColumn<PedidoTransferenciaItem>() {

            @Override
            public Component getComponent(String componentId, final PedidoTransferenciaItem rowObject) {
                return new DetalhesActionColumnPanel(componentId) {

                    @Override
                    public void onDetalhar(AjaxRequestTarget target) {
                        dialogDetalhesItem.setModelObject(rowObject);
                        dialogDetalhesItem.update(target);
                        dialogDetalhesItem.show(target);
                    }
                };
            }
        };
    }

    private List<IColumn> getEditColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferenciaItem.class);
        PedidoTransferenciaItem proxy = on(PedidoTransferenciaItem.class);

        columns.add(getEditCustomColumn());
        columns.add(columnFactory.createColumn(bundle("produto"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(bundle("un"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(bundle("qtSolicitada"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE_SOLICITADA)));
        columns.add(columnFactory.createColumn(bundle("situacao"), VOUtils.montarPath(PedidoTransferenciaItem.PROP_DESCRICAO_STATUS)));

        return columns;

    }

    private IColumn getEditCustomColumn() {
        return new MultipleActionCustomColumn<PedidoTransferenciaItem>() {
            @Override
            public void customizeColumn(PedidoTransferenciaItem rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<PedidoTransferenciaItem>() {
                    @Override
                    public void action(AjaxRequestTarget target, PedidoTransferenciaItem modelObject) throws ValidacaoException, DAOException {
                        dialogDetalhesItem.setModelObject(rowObject);
                        dialogDetalhesItem.update(target);
                        dialogDetalhesItem.show(target);
                    }


                }).setTitleBundleKey("consultar");
                if(verificarUsuarioControlador()) {
                    addAction(ActionType.APROVAR_PEDIDO_TRANSF, rowObject, new IModelAction<PedidoTransferenciaItem>() {
                        @Override
                        public void action(AjaxRequestTarget target, PedidoTransferenciaItem modelObject) throws ValidacaoException, DAOException {
                            if (modelObject.getPedidoTransferencia().getEmpresa() == null) {
                                modelObject = getItem(modelObject.getCodigo());
                            }
                            modelObject.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value());
                            BOFactory.save(modelObject);
                            if (!verificarPedidoTranfencia()) {
                                pedidoTransferencia.setStatus(PedidoTransferencia.STATUS_ABERTO);
                                BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).update(pedidoTransferencia);
                            }
                            table.update(target);
                        }
                    }).setTitleBundleKey("aprovar")
                            .setIcon(Icon.CHECKMARK)
                            .setVisible(rowObject.getStatus().equals(PedidoTransferencia.STATUS_PENDENTE_APROVACAO));

                    addAction(ActionType.REPROVAR_PEDIDO_TRANSF, rowObject, new IModelAction<PedidoTransferenciaItem>() {
                        @Override
                        public void action(AjaxRequestTarget target, PedidoTransferenciaItem modelObject) throws ValidacaoException, DAOException {
                            if (modelObject.getPedidoTransferencia().getEmpresa() == null) {
                                modelObject = getItem(modelObject.getCodigo());
                            }
                            modelObject.setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value());
                            BOFactoryWicket.save(modelObject);
                            if (!verificarPedidoTranfencia()) {
                                if (cancelarAutomatico()) {
                                    pedidoTransferencia.setStatus(PedidoTransferencia.STATUS_CANCELADO);
                                } else {
                                    pedidoTransferencia.setStatus(PedidoTransferencia.STATUS_ABERTO);
                                }
                                BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).update(pedidoTransferencia);
                            }
                            table.update(target);
                        }
                    }).setTitleBundleKey("reprovar")
                            .setVisible(rowObject.getStatus().equals(PedidoTransferencia.STATUS_PENDENTE_APROVACAO));
                }
            }
        };
    }

    private CollectionProvider getCollectionProvider() {
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider() {

                @Override
                public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                    return getItens();
                }
            };
        }
        return this.collectionProvider;
    }

    private List<PedidoTransferenciaItem> getItens() throws DAOException, ValidacaoException {
        return LoadManager.getInstance(PedidoTransferenciaItem.class)
                .addProperties(new HQLProperties(PedidoTransferenciaItem.class).getProperties())
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CANCELAMENTO, Usuario.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CANCELAMENTO, Usuario.PROP_LOGIN))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CANCELAMENTO, Usuario.PROP_NOME))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_REFERENCIA))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA), pedidoTransferencia))
                .addSorter(new QueryCustom.QueryCustomSorter(PedidoTransferenciaItem.PROP_ITEM))
                .start().getList();
    }

    private PedidoTransferenciaItem getItem(Long codigo){
        return LoadManager.getInstance(PedidoTransferenciaItem.class)
                .addProperties(new HQLProperties(PedidoTransferenciaItem.class).getProperties())
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA,PedidoTransferencia.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA,PedidoTransferencia.PROP_EMPRESA_DESTINO, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA,PedidoTransferencia.PROP_EMPRESA_ORIGEM, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA,PedidoTransferencia.PROP_DATA_PEDIDO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_CODIGO),codigo))
                .start().getVO();
    }

    private boolean verificarPedidoTranfencia() throws DAOException, ValidacaoException {
        boolean pedidoPermanecePendente = false;
        for (PedidoTransferenciaItem item : getItens()) {
            if (item.getStatus().equals(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.PENDENTE_APROVACAO.value())) {
                pedidoPermanecePendente = true;
            }
        }
        return pedidoPermanecePendente;
    }

    private boolean cancelarAutomatico() throws DAOException, ValidacaoException {
        boolean cancelar = false;
        int verificarCancelados = 0;
        List<PedidoTransferenciaItem> itens = getItens();
        Integer tamanhoTotal = itens.size();
        for (PedidoTransferenciaItem item : itens) {
            if (item.getStatus().equals(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value())) {
                verificarCancelados +=1;
            }
        }
        if(tamanhoTotal == verificarCancelados){
            cancelar = true;
        }
        return cancelar;
    }

    protected boolean verificarUsuarioControlador(){
        boolean controlador = false;
        Usuario usuario = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
        if (usuario.isNivelAdminOrMaster()) {
            controlador = true;
        }else {
            List<UsuarioGrupo> usuarioGrupos = LoadManager.getInstance(UsuarioGrupo.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(path(on(UsuarioGrupo.class).getUsuario().getCodigo()), ApplicationSession.get().getSessaoAplicacao().getUsuario().getCodigo()))
                    .start().getList();
            for (UsuarioGrupo usuarioGrupo : usuarioGrupos) {
                if (usuarioGrupo.getGrupo().getNome().equals(CONTROLADOR_BRANET))
                    controlador = true;
            }
        }
        return controlador;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesPedido");
    }

}
