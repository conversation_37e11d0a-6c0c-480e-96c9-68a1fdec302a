package br.com.celk.view.comunicacao.mensagem.panel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.panel.ViewPanel;
import br.com.celk.component.uploadfield.MultiFileUploadFieldAnexo;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.comunicacao.mensagem.IMensagemController;
import br.com.celk.view.comunicacao.mensagem.autocomplete.MessagingAutoCompleteGrupoMensagem;
import br.com.celk.view.comunicacao.mensagem.autocomplete.MessagingAutoCompleteUsuario;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.MultiFileUploadField;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.core.javascript.JsQuery;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class EnviarMensagemPanel extends ViewPanel implements IMensagemPanel{

    private Form<MensagemDTO> form;
    private MessagingAutoCompleteUsuario messagingAutoCompleteUsuario;
    private InputField txtAssunto;
    private InputArea txaMensagem;
    private MultiFileUploadField mfuAnexo;
    
    private final Collection<FileUpload> uploads = new ArrayList<FileUpload>();
    private IMensagemController mensagemController;
    
    private String CSS_FILE = "EnviarMensagemPanel.css";
    
    public EnviarMensagemPanel(String id) {
        super(id);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        
        MensagemDTO mensagemProxy = on(MensagemDTO.class);
        
        getForm().add(messagingAutoCompleteUsuario = new MessagingAutoCompleteUsuario(path(mensagemProxy.getUsuarios())));
        messagingAutoCompleteUsuario.setFiltrarUsuariosAtivos(true);
        getForm().add(new MessagingAutoCompleteGrupoMensagem(path(mensagemProxy.getGrupos())));
        getForm().add(txtAssunto = new InputField(path(mensagemProxy.getAssunto())));
                
        Form formAnexo = new Form("formAnexo");
        formAnexo.add(new Label("mensagemAnexo", BundleManager.getString("msgTamanhoMaximoTotalAnexoMensagem", getTamanhoAnexoMensagem())).setOutputMarkupId(true));
        formAnexo.setMultiPart(true);
        formAnexo.add(mfuAnexo = new MultiFileUploadFieldAnexo("uploads", new PropertyModel<Collection<FileUpload>>(this, "uploads")));
        getForm().add(formAnexo);
        
        getForm().add(txaMensagem = new InputArea(path(mensagemProxy.getMensagem())));
        
        getForm().add(new AbstractAjaxButton("linkEnviar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                MensagemDTO dto = (MensagemDTO) getForm().getModel().getObject();
                
                if (StringUtils.trimToNull(dto.getMensagem())==null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_informe_corpo_mensagem"));
                }
                if (StringUtils.trimToNull(dto.getAssunto())==null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_informe_assunto_mensagem"));
                }
                if (!CollectionUtils.isNotNullEmpty(dto.getUsuarios()) && !CollectionUtils.isNotNullEmpty(dto.getGrupos())) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_informe_destino_mensagem"));
                }
                
                Long sizeUpload = 0L;
                for (FileUpload upload : uploads) {
                    sizeUpload += upload.getSize();
                }
                
                if(sizeUpload.compareTo(getTamanhoAnexoMensagem()*1024) > 0){
                    throw new ValidacaoException(BundleManager.getString("msgTamanhoMaximoTotalAnexoMensagem", getTamanhoAnexoMensagem()));
                }
                                
                for (FileUpload upload : uploads) {
                    try {
                        MensagemAnexoDTO msgAnexoDTO = new MensagemAnexoDTO();
                        
                        File newFile = File.createTempFile("anexo" , upload.getClientFileName());
                        upload.writeTo(newFile);
                        
                        msgAnexoDTO.setNomeArquivoOriginal(upload.getClientFileName());
                        msgAnexoDTO.setNomeArquivoUpload(newFile.getAbsolutePath());
                        
                        dto.getUploadList().add(msgAnexoDTO);
                    } catch (IOException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }
                BOFactoryWicket.getBO(ComunicacaoFacade.class).enviarMensagem(dto);
                ApplicationSession.get().adicinarQuantidadeMensagensUsuario();
                mensagemController.caixaEntrada(target);
            }
        });
        
        add(getForm());
    }
    
    private Long getTamanhoAnexoMensagem(){
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("tamanhoAnexoMensagem");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return 0L;
    }
    
    private Form<MensagemDTO> getForm(){
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(new MensagemDTO()));
        }
        
        return this.form;
    }
    
    @Override
    public void setMensagemController(IMensagemController mensagemController) {
        this.mensagemController = mensagemController;
    }
    
    public void encaminharMensagem(Mensagem mensagem){
        getForm().getModelObject().setMensagem(prepararConteudo(mensagem));
        getForm().getModelObject().setAssunto("Enc. "+mensagem.getAssunto());
    }
    
    public void responderMensagem(Mensagem mensagem){
        getForm().getModelObject().setUsuarios(Arrays.asList(mensagem.getMensagemOrigem().getUsuario()));
        getForm().getModelObject().setMensagem(prepararConteudo(mensagem));
        getForm().getModelObject().setAssunto("Re. "+mensagem.getAssunto());
    }
    
    private String prepararConteudo(Mensagem mensagem){
        String lineSeparator = System.getProperty("line.separator");
        
        StringBuilder sb = new StringBuilder();
        
        sb.append("Em ");
        sb.append(Data.formatarDataHora(mensagem.getData()));
        sb.append(", ");
        if (mensagem.resolverTipo().equals(Mensagem.Tipo.ENTRADA)) {
            sb.append(mensagem.getMensagemOrigem().getUsuario().getNome());
        } else {
            sb.append(mensagem.getUsuario().getNome());
        }
        sb.append(" escreveu:");
        sb.append(lineSeparator);
        sb.append(" > ");
        sb.append(mensagem.getMensagem().replaceAll("("+lineSeparator+")", "$1 > "));
        sb.append(lineSeparator);
        sb.append(lineSeparator);
        
        return sb.toString();
    }

    @Override
    public void atualizarMensagens(AjaxRequestTarget target) {
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnLoadHeaderItem.forScript(new JsQuery(messagingAutoCompleteUsuario.getTextField()).$().chain("focus").getStatement()));
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
    }

}
