package br.com.celk.view.comunicacao.mensagem.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.panel.ViewPanel;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.IPagingBar;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.resources.Icon16;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.comunicacao.mensagem.IMensagemController;
import br.com.celk.view.comunicacao.mensagem.table.EntradaTableRow;
import br.com.celk.view.comunicacao.mensagem.table.MessagingPagingBar;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTOParam;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.hospital.AihMensagem;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class EntradaPanel extends ViewPanel implements IMensagemPanel {

    private final String VIEW_PANEL_ID = "viewPanel";

    private WebMarkupContainer viewContainer;
    private SelectionPageableTable<QueryConsultaMensagensDTO> pageableTable;
    private IMensagemController mensagemController;
    private Panel activePanel;
    private String usuario;
    private Empresa unidade;
    private List<AihMensagem> mensagensAih;

    public EntradaPanel(String id) {
        super(id);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        carregarMensagensAih();

        Form form = new Form("form");

        form.add(new InputField("filtroUsuario", new PropertyModel<String>(this, "usuario")));
        form.add(new AutoCompleteConsultaEmpresa("empresa", new PropertyModel<Empresa>(this, "unidade")));

        form.add(pageableTable = new SelectionPageableTable("tblMensagens", getColumns(), getPagerProvider(), 50) {

            @Override
            public IPagingBar newPagingBar(String id, PageableTable pageableTable) {
                return new MessagingPagingBar(id, pageableTable);
            }

            @Override
            protected Item newRowItem(String id, int index, IModel model) {
                return new EntradaTableRow(id, index, model, pageableTable) {

                    @Override
                    public void onSelection(AjaxRequestTarget target) {
                        super.onSelection(target); //To change body of generated methods, choose Tools | Templates.
                        mensagemController.marcarComoLida(target, getModelObject());
                        target.add(this);
                    }
                };
            }

            @Override
            public void update(AjaxRequestTarget target) {
                super.update(target); //To change body of generated methods, choose Tools | Templates.
                Component get = viewContainer.get(VIEW_PANEL_ID);
                get.replaceWith(new EmptyPanel(VIEW_PANEL_ID));
                target.add(viewContainer);
            }
        });
        pageableTable.setScrollY("200px");
        pageableTable.setColumnsDefs("[{ \"sWidth\": \"5px\", \"aTargets\": [ 0 ] }]");
        pageableTable.setScrollCollapse(false);
        pageableTable.populate();

        pageableTable.addSelectionAction(new ISelectionAction<QueryConsultaMensagensDTO>() {

            @Override
            public void onSelection(AjaxRequestTarget target, QueryConsultaMensagensDTO object) {
                visualizarMensagem(target, object);
            }
        });

        ProcurarButton procurarButton;
        form.add(procurarButton = new ProcurarButton("btnProcurar", pageableTable) {
            @Override
            public Object getParam() {
                return getParameter();
            }
        });
        procurarButton.procurar();

        add(viewContainer = new WebMarkupContainer("viewContainer"));
        viewContainer.setOutputMarkupId(true);

        if (activePanel == null) {
            activePanel = new EmptyPanel(VIEW_PANEL_ID);
        }

        viewContainer.add(activePanel);

        if (activePanel instanceof VisualizarMensagemPanel) {
            pageableTable.setSelectedObject(((VisualizarMensagemPanel) activePanel).getDTO());
        }

        add(form);
    }

    public void setMensagem(QueryConsultaMensagensDTO dto) {
        VisualizarMensagemPanel visualizarMensagemPanel = new VisualizarMensagemPanel(VIEW_PANEL_ID, mensagemController.marcarComoLida(null, dto));
        activePanel = visualizarMensagemPanel;
        visualizarMensagemPanel.setMensagemController(mensagemController);
    }

    private void carregarMensagensAih() {
        AihMensagem proxy = on(AihMensagem.class);
        Usuario usuarioLogado = ApplicationSession.get().getSession().<Usuario>getUsuario();

        mensagensAih = LoadManager.getInstance(AihMensagem.class)
                .addProperties(new HQLProperties(AihMensagem.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AihMensagem.PROP_MENSAGEM, Mensagem.PROP_USUARIO),
                        usuarioLogado))
                .start().getList();
    }

    private List<AihMensagem> getMensagensAih() {
        return mensagensAih;
    }

    private void visualizarMensagem(QueryConsultaMensagensDTO dto) {
        Component get = viewContainer.get(VIEW_PANEL_ID);
        VisualizarMensagemPanel panel;

        dto.setTipoMensagem(QueryConsultaMensagensDTO.PROP_RECEBIDOS);
        get.replaceWith(panel = new VisualizarMensagemPanel(VIEW_PANEL_ID, dto));

        panel.setMensagemController(mensagemController);
    }

    private void visualizarMensagem(AjaxRequestTarget target, QueryConsultaMensagensDTO dto) {
        visualizarMensagem(dto);

        target.add(viewContainer);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        QueryConsultaMensagensDTO on = on(QueryConsultaMensagensDTO.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("lido"), on.getMensagem().getLida(), on.getMensagem().getLidoFormatado()));
        columns.add(createSortableColumn(bundle("usuario"), on.getMensagem().getMensagemOrigem().getUsuario().getNome()));
        columns.add(createSortableColumn(bundle("funcao"), on.getFuncao()));
        columns.add(createSortableColumn(bundle("unidade"), on.getUnidade()));
        columns.add(createSortableColumn(bundle("assunto"), on.getMensagem().getAssunto()));
        columns.add(new DateColumn(bundle("data"), path(on.getMensagem().getData()), path(on.getMensagem().getData())).setPattern("dd/MM/yyyy - HH:mm"));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<QueryConsultaMensagensDTO>() {

            @Override
            public void customizeColumn(QueryConsultaMensagensDTO rowObject) {
                Boolean adicionarBotaoRemover = true;

                for (AihMensagem mensagemAih: mensagensAih) {
                    if (rowObject.getMensagem().getCodigo().equals(mensagemAih.getMensagem().getCodigo())) {
                        adicionarBotaoRemover = false;
                    }
                }

                if (adicionarBotaoRemover) {
                    addAction(ActionType.REMOVER, rowObject, new IModelAction<QueryConsultaMensagensDTO>() {
                        @Override
                        public void action(AjaxRequestTarget target, QueryConsultaMensagensDTO modelObject) throws ValidacaoException, DAOException {
                            if (!RepositoryComponentDefault.SimNaoLong.SIM.value().equals(modelObject.getMensagem().getLida())){
                                ApplicationSession.get().subtrairQuantidadeMensagensUsuario();
                            }
                            modelObject.getMensagem().setExcluida(RepositoryComponentDefault.SimNaoLong.SIM.value());
                            modelObject.setMensagem(BOFactoryWicket.save(modelObject.getMensagem()));
                            mensagemController.notificarMensagem(target, modelObject);
                            atualizarMensagens(target);
                        }
                    }).setIcon(Icon16.cross);
                }
            }
        };
    }

    private IPagerProvider getPagerProvider() {
        return new QueryPagerProvider<QueryConsultaMensagensDTO, QueryConsultaMensagensDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<QueryConsultaMensagensDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setPropOrdenacao(((SingleSortState<String>) getSortState()).getSort().getProperty());
                dataPaging.getParam().setAsc(((SingleSortState<String>) getSortState()).getSort().isAscending());

                return BOFactoryWicket.getBO(ComunicacaoFacade.class).consultarMensagens(dataPaging);
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(path(on(QueryConsultaMensagensDTO.class).getMensagem().getData()), false);
            }
        };
    }

    private QueryConsultaMensagensDTOParam getParameter() {
        QueryConsultaMensagensDTOParam param = new QueryConsultaMensagensDTOParam();

        param.setTipo(Mensagem.Tipo.ENTRADA);
        param.setDe(usuario);
        param.setExcluida(false);
        param.setUsuario(ApplicationSession.get().getSession().<Usuario>getUsuario());
        param.setUnidade(unidade);

        return param;
    }

    @Override
    public void atualizarMensagens(AjaxRequestTarget target) {
        pageableTable.update(target);
    }

    @Override
    public void setMensagemController(IMensagemController mensagemController) {
        this.mensagemController = mensagemController;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
    }
}