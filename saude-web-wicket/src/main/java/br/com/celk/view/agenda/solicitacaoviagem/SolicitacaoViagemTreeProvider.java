package br.com.celk.view.agenda.solicitacaoviagem;

import br.com.celk.component.treetable.pageable.PageableTreeProvider;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class SolicitacaoViagemTreeProvider extends PageableTreeProvider<SolicitacaoViagemDTO, String> {

    private SolicitacaoViagemDTOParam param;

    public SolicitacaoViagemTreeProvider() {
    }

    public SolicitacaoViagemTreeProvider(SolicitacaoViagemDTOParam param) {
        this.param = param;
        carregar();
    }

    private void carregar() {
        getRootList().clear();
        SolicitacaoViagem proxy = on(SolicitacaoViagem.class);

        setRootList(new ArrayList<SolicitacaoViagemDTO>());

        LoadManager lm = LoadManager.getInstance(SolicitacaoViagem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getLocal()), QueryCustom.QueryCustomParameter.CONSULTA_LIKED, this.param.getLocal()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoViagemPrincipal().getStatus()), this.param.getSituacao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCidade()), this.param.getDestino()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getTipoTransporteViagem()), this.param.getTipoTransporteViagem()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDataViagem()), QueryCustom.QueryCustomParameter.MAIOR_IGUAL, this.param.getDataViagem()))
                .addProperties(new HQLProperties(SolicitacaoViagem.class).getProperties())
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DATA_NASCIMENTO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_ACOMPANHANTE, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_ACOMPANHANTE, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_ACOMPANHANTE, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_ACOMPANHANTE, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_ACOMPANHANTE, UsuarioCadsus.PROP_DATA_NASCIMENTO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_ACOMPANHANTE, UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_CIDADE, Cidade.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_CIDADE, Cidade.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_TIPO_TRANSPORTE_VIAGEM, TipoTransporteViagem.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_TIPO_TRANSPORTE_VIAGEM, TipoTransporteViagem.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_SOLICITACAO_VIAGEM_PRINCIPAL, SolicitacaoViagem.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(SolicitacaoViagem.PROP_SOLICITACAO_VIAGEM_PRINCIPAL, SolicitacaoViagem.PROP_STATUS))
                .addSorter(new QueryCustom.QueryCustomSorter(SolicitacaoViagem.PROP_DATA_VIAGEM, "asc"));
        if (param.getPaciente() != null) {
            lm.addParameter(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getNome()), BuilderQueryCustom.QueryParameter.ILIKE, param.getPaciente()))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getUtilizaNomeSocial()), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus().getApelido()), BuilderQueryCustom.QueryParameter.ILIKE, param.getPaciente()))))));
        }

        List<SolicitacaoViagem> lstSolicitacaoViagem = lm.startLeitura().getList();

        Group<SolicitacaoViagem> group = Lambda.group(lstSolicitacaoViagem, by(on(SolicitacaoViagem.class).getSolicitacaoViagemPrincipal()));

        for (Group<SolicitacaoViagem> solicitacaoViagemGroup : group.subgroups()) {
            SolicitacaoViagemDTO dtoPai = new SolicitacaoViagemDTO();
            for (SolicitacaoViagem sv : solicitacaoViagemGroup.findAll()) {
                dtoPai.setSolicitacaoViagem(sv);

                if (sv.getAcompanhante() != null) {
                    if (dtoPai.getFilhos() == null || dtoPai.getFilhos().isEmpty()) {
                        SolicitacaoViagemDTO dtoHeader = new SolicitacaoViagemDTO();
                        dtoHeader.setHeader(true);
                        dtoHeader.getHeaders().add(BundleManager.getString("acompanhante"));
                        dtoPai.addFilhos(dtoHeader);
                    }
                    SolicitacaoViagemDTO dtoFilho = new SolicitacaoViagemDTO();
                    dtoFilho.setSolicitacaoViagemLeaf(sv);
                    dtoFilho.setPai(dtoPai);
                    dtoPai.addFilhos(dtoFilho);
                }
            }
            getRootList().add(dtoPai);
        }
    }

    public void recarregar(SolicitacaoViagemDTOParam param) {
        this.param = param;
        carregar();
    }

}
