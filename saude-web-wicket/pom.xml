<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>saude-2</artifactId>
        <groupId>br.com.celk</groupId>
<version>3.1.282.2-SNAPSHOT</version>
    </parent>

    <artifactId>saude-web-wicket</artifactId>
    <packaging>war</packaging>
    <name>saude-web-wicket</name>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.zeroturnaround</groupId>
                <artifactId>jrebel-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Gem-Version>${project.version}</Gem-Version>
                            <ComponentPath>${build.type}</ComponentPath>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jarsigner-plugin</artifactId>
                <executions>
                    <execution>
                        <id>sign</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>sign</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <archiveDirectory>${project.build.directory}/${project.artifactId}-${project.version}/applets
                    </archiveDirectory>
                    <includes>
                        <include>*.jar</include>
                    </includes>
                    <keystore>../resources/ksi.jks</keystore>
                    <keypass>${certificado.keypass}</keypass>
                    <storepass>${certificado.storepass}</storepass>
                    <alias>${certificado.alias}</alias>
                    <storetype>${certificado.storetype}</storetype>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>applet</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>br.com.celk.biometria</groupId>
                                    <artifactId>NBioBSPJNI</artifactId>
                                    <version>1.0.0.3</version>
                                    <type>jar</type>
                                </artifactItem>

                                <artifactItem>
                                    <groupId>br.com.celk.jzebra</groupId>
                                    <artifactId>jssc_qz</artifactId>
                                    <version>0.1</version>
                                    <type>jar</type>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>br.com.celk.jzebra</groupId>
                                    <artifactId>pdf-render_qz</artifactId>
                                    <version>0.1</version>
                                    <type>jar</type>
                                </artifactItem>
                            </artifactItems>
                            <outputDirectory>
                                ${project.build.directory}/${project.artifactId}-${project.version}/applets
                            </outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!--  WICKET DEPENDENCIES -->
        <dependency>
            <artifactId>wicket-core</artifactId>
            <groupId>org.apache.wicket</groupId>
        </dependency>
        <dependency>
            <groupId>org.odlabs.wiquery</groupId>
            <artifactId>wiquery-jquery-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.wicket</groupId>
            <artifactId>wicket-extensions</artifactId>
        </dependency>
        <dependency>
            <groupId>org.wicketstuff</groupId>
            <artifactId>wicketstuff-openlayers</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.wicket</groupId>
            <artifactId>wicket-devutils</artifactId>
        </dependency>

        <dependency>
            <groupId>org.wicketstuff</groupId>
            <artifactId>wicketstuff-tinymce</artifactId>
        </dependency>
        <dependency>
            <groupId>org.wicketstuff</groupId>
            <artifactId>wicketstuff-annotation</artifactId>
        </dependency>

        <!-- LOGGING DEPENDENCIES - LOG4J -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
        </dependency>
        <!-- CELK DEPENDENCIES            -->
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-connect</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-utils</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>br.com.celk</groupId>
            <artifactId>saude-core</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>javax.enterprise</groupId>
            <artifactId>cdi-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.spec.javax.jms</groupId>
            <artifactId>jboss-jms-api_2.0_spec</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.spec.javax.ejb</groupId>
            <artifactId>jboss-ejb-api_3.2_spec</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.spec.javax.websocket</groupId>
            <artifactId>jboss-websocket-api_1.0_spec</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <artifactId>commons-collections</artifactId>
            <groupId>commons-collections</groupId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.as</groupId>
            <artifactId>jboss-as-messaging</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>saude-web-processors</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>saude-wicket-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-multipart-provider</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.wicketstuff</groupId>
            <artifactId>wicketstuff-html5</artifactId>
        </dependency>
        <dependency>
            <groupId>org.wicketstuff</groupId>
            <artifactId>wicketstuff-gmap3</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
